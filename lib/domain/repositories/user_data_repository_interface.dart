import '../../data/enums/media_type_enum.dart';
import '../../data/models/user_data_models.dart';

abstract class UserDataRepositoryInterface {
  Future<bool> toggleFavorite({
    required String userId,
    required String itemId,
    required MediaType itemType, // Changed from String to MediaType
  });

  Future<bool> isFavorite({
    required String userId,
    required String itemId,
  });

  Future<List<FavoriteItem>> getFavorites({
    required String userId,
  });

  Future<bool> toggleBookmark({
    required String userId,
    required String itemId,
    required MediaType itemType, // Changed from String to MediaType
  });

  Future<bool> isBookmarked({
    required String userId,
    required String itemId,
  });

  Future<List<BookmarkItem>> getBookmarks({
    required String userId,
  });

  Future<void> clearUserData({
    required String userId,
    bool clearFavorites = true,
    bool clearBookmarks = true,
  });

  Future<void> transferAnonymousData({
    required String userId,
  });
}
