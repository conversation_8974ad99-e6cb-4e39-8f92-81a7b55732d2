// lib/data/models/category_model.dart

import 'package:freezed_annotation/freezed_annotation.dart';

import 'content_model.dart';

part 'category_model.freezed.dart';
part 'category_model.g.dart';

@freezed
abstract class CategoryModel with _$CategoryModel {
  const factory CategoryModel({
    required int id,
    required String name,
    @JsonKey(name: 'parent_id') int? parentId,
    @JsonKey(name: 'subcategories_count') int? subcategoriesCount,
    List<CategoryModel>? subcategories,
    List<ContentModel>? contents,
  }) = _CategoryModel;

  factory CategoryModel.fromJson(Map<String, dynamic> json) =>
      _$CategoryModelFromJson(json);
}
