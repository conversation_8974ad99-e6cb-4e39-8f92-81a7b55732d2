// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'category_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CategoryModel {
  int get id;
  String get name;
  @JsonKey(name: 'parent_id')
  int? get parentId;
  @JsonKey(name: 'subcategories_count')
  int? get subcategoriesCount;
  List<CategoryModel>? get subcategories;
  List<ContentModel>? get contents;

  /// Create a copy of CategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CategoryModelCopyWith<CategoryModel> get copyWith =>
      _$CategoryModelCopyWithImpl<CategoryModel>(
          this as CategoryModel, _$identity);

  /// Serializes this CategoryModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CategoryModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.subcategoriesCount, subcategoriesCount) ||
                other.subcategoriesCount == subcategoriesCount) &&
            const DeepCollectionEquality()
                .equals(other.subcategories, subcategories) &&
            const DeepCollectionEquality().equals(other.contents, contents));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      parentId,
      subcategoriesCount,
      const DeepCollectionEquality().hash(subcategories),
      const DeepCollectionEquality().hash(contents));

  @override
  String toString() {
    return 'CategoryModel(id: $id, name: $name, parentId: $parentId, subcategoriesCount: $subcategoriesCount, subcategories: $subcategories, contents: $contents)';
  }
}

/// @nodoc
abstract mixin class $CategoryModelCopyWith<$Res> {
  factory $CategoryModelCopyWith(
          CategoryModel value, $Res Function(CategoryModel) _then) =
      _$CategoryModelCopyWithImpl;
  @useResult
  $Res call(
      {int id,
      String name,
      @JsonKey(name: 'parent_id') int? parentId,
      @JsonKey(name: 'subcategories_count') int? subcategoriesCount,
      List<CategoryModel>? subcategories,
      List<ContentModel>? contents});
}

/// @nodoc
class _$CategoryModelCopyWithImpl<$Res>
    implements $CategoryModelCopyWith<$Res> {
  _$CategoryModelCopyWithImpl(this._self, this._then);

  final CategoryModel _self;
  final $Res Function(CategoryModel) _then;

  /// Create a copy of CategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? parentId = freezed,
    Object? subcategoriesCount = freezed,
    Object? subcategories = freezed,
    Object? contents = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      parentId: freezed == parentId
          ? _self.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as int?,
      subcategoriesCount: freezed == subcategoriesCount
          ? _self.subcategoriesCount
          : subcategoriesCount // ignore: cast_nullable_to_non_nullable
              as int?,
      subcategories: freezed == subcategories
          ? _self.subcategories
          : subcategories // ignore: cast_nullable_to_non_nullable
              as List<CategoryModel>?,
      contents: freezed == contents
          ? _self.contents
          : contents // ignore: cast_nullable_to_non_nullable
              as List<ContentModel>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _CategoryModel implements CategoryModel {
  const _CategoryModel(
      {required this.id,
      required this.name,
      @JsonKey(name: 'parent_id') this.parentId,
      @JsonKey(name: 'subcategories_count') this.subcategoriesCount,
      final List<CategoryModel>? subcategories,
      final List<ContentModel>? contents})
      : _subcategories = subcategories,
        _contents = contents;
  factory _CategoryModel.fromJson(Map<String, dynamic> json) =>
      _$CategoryModelFromJson(json);

  @override
  final int id;
  @override
  final String name;
  @override
  @JsonKey(name: 'parent_id')
  final int? parentId;
  @override
  @JsonKey(name: 'subcategories_count')
  final int? subcategoriesCount;
  final List<CategoryModel>? _subcategories;
  @override
  List<CategoryModel>? get subcategories {
    final value = _subcategories;
    if (value == null) return null;
    if (_subcategories is EqualUnmodifiableListView) return _subcategories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<ContentModel>? _contents;
  @override
  List<ContentModel>? get contents {
    final value = _contents;
    if (value == null) return null;
    if (_contents is EqualUnmodifiableListView) return _contents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  /// Create a copy of CategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CategoryModelCopyWith<_CategoryModel> get copyWith =>
      __$CategoryModelCopyWithImpl<_CategoryModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CategoryModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CategoryModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.subcategoriesCount, subcategoriesCount) ||
                other.subcategoriesCount == subcategoriesCount) &&
            const DeepCollectionEquality()
                .equals(other._subcategories, _subcategories) &&
            const DeepCollectionEquality().equals(other._contents, _contents));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      name,
      parentId,
      subcategoriesCount,
      const DeepCollectionEquality().hash(_subcategories),
      const DeepCollectionEquality().hash(_contents));

  @override
  String toString() {
    return 'CategoryModel(id: $id, name: $name, parentId: $parentId, subcategoriesCount: $subcategoriesCount, subcategories: $subcategories, contents: $contents)';
  }
}

/// @nodoc
abstract mixin class _$CategoryModelCopyWith<$Res>
    implements $CategoryModelCopyWith<$Res> {
  factory _$CategoryModelCopyWith(
          _CategoryModel value, $Res Function(_CategoryModel) _then) =
      __$CategoryModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int id,
      String name,
      @JsonKey(name: 'parent_id') int? parentId,
      @JsonKey(name: 'subcategories_count') int? subcategoriesCount,
      List<CategoryModel>? subcategories,
      List<ContentModel>? contents});
}

/// @nodoc
class __$CategoryModelCopyWithImpl<$Res>
    implements _$CategoryModelCopyWith<$Res> {
  __$CategoryModelCopyWithImpl(this._self, this._then);

  final _CategoryModel _self;
  final $Res Function(_CategoryModel) _then;

  /// Create a copy of CategoryModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? parentId = freezed,
    Object? subcategoriesCount = freezed,
    Object? subcategories = freezed,
    Object? contents = freezed,
  }) {
    return _then(_CategoryModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      parentId: freezed == parentId
          ? _self.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as int?,
      subcategoriesCount: freezed == subcategoriesCount
          ? _self.subcategoriesCount
          : subcategoriesCount // ignore: cast_nullable_to_non_nullable
              as int?,
      subcategories: freezed == subcategories
          ? _self._subcategories
          : subcategories // ignore: cast_nullable_to_non_nullable
              as List<CategoryModel>?,
      contents: freezed == contents
          ? _self._contents
          : contents // ignore: cast_nullable_to_non_nullable
              as List<ContentModel>?,
    ));
  }
}

// dart format on
