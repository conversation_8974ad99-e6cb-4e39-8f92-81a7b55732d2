import 'package:freezed_annotation/freezed_annotation.dart';

import 'category_model.dart';
import 'content_model.dart';

part 'api_response.freezed.dart';
part 'api_response.g.dart';

@freezed
abstract class CategoryContentData with _$CategoryContentData {
  const factory CategoryContentData({
    required CategoryModel category,
    required List<ContentModel> contents,
    required PaginationInfo pagination,
  }) = _CategoryContentData;

  factory CategoryContentData.fromJson(Map<String, dynamic> json) =>
      _$CategoryContentDataFromJson(json);
}

// --- For the /categories/{id}/contents endpoint ---
@freezed
abstract class PaginatedContentResponse with _$PaginatedContentResponse {
  const factory PaginatedContentResponse({
    required CategoryContentData data,
  }) = _PaginatedContentResponse;

  factory PaginatedContentResponse.fromJson(Map<String, dynamic> json) =>
      _$PaginatedContentResponseFromJson(json);
}

@freezed
abstract class PaginationInfo with _$PaginationInfo {
  const factory PaginationInfo({
    @JsonKey(name: 'current_page') required int currentPage,
    @JsonKey(name: 'per_page') required int perPage,
    required int total,
    @JsonKey(name: 'last_page') required int lastPage,
    int? from,
    int? to,
    @JsonKey(name: 'has_more') required bool hasMore,
  }) = _PaginationInfo;

  factory PaginationInfo.fromJson(Map<String, dynamic> json) =>
      _$PaginationInfoFromJson(json);
}

// --- For the /content/search endpoint ---
@freezed
abstract class SearchResultsResponse with _$SearchResultsResponse {
  const factory SearchResultsResponse({
    @JsonKey(name: 'search_query') String? searchQuery,
    @JsonKey(name: 'total_results') required int totalResults,
    @JsonKey(name: 'categories_with_results')
    required int categoriesWithResults,
    required List<CategorySearchResult> results,
  }) = _SearchResultsResponse;

  factory SearchResultsResponse.fromJson(Map<String, dynamic> json) =>
      _$SearchResultsResponseFromJson(json);
}

@freezed
abstract class CategorySearchResult with _$CategorySearchResult {
  const factory CategorySearchResult({
    required CategoryModel category,
    required List<ContentModel> contents,
    @JsonKey(name: 'content_count') required int contentCount,
  }) = _CategorySearchResult;

  factory CategorySearchResult.fromJson(Map<String, dynamic> json) =>
      _$CategorySearchResultFromJson(json);
}
