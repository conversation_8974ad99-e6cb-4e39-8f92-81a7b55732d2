// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'content_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ContentModel _$ContentModelFromJson(Map<String, dynamic> json) =>
    _ContentModel(
      id: (json['id'] as num).toInt(),
      title: json['title'] as String,
      description: json['description'] as String?,
      contentType: json['content_type'] as String,
      contentTypeLabel: json['content_type_label'] as String,
      visits: (json['visits'] as num).toInt(),
      categoryId: (json['category_id'] as num).toInt(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      audioData: json['audio_data'] == null
          ? null
          : AudioData.fromJson(json['audio_data'] as Map<String, dynamic>),
      videoData: json['video_data'] == null
          ? null
          : VideoData.fromJson(json['video_data'] as Map<String, dynamic>),
      articleData: json['article_data'] == null
          ? null
          : ArticleData.fromJson(json['article_data'] as Map<String, dynamic>),
      bookData: json['book_data'] == null
          ? null
          : BookData.fromJson(json['book_data'] as Map<String, dynamic>),
      postData: json['post_data'] == null
          ? null
          : PostData.fromJson(json['post_data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ContentModelToJson(_ContentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'content_type': instance.contentType,
      'content_type_label': instance.contentTypeLabel,
      'visits': instance.visits,
      'category_id': instance.categoryId,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'audio_data': instance.audioData,
      'video_data': instance.videoData,
      'article_data': instance.articleData,
      'book_data': instance.bookData,
      'post_data': instance.postData,
    };

_AudioData _$AudioDataFromJson(Map<String, dynamic> json) => _AudioData(
      id: (json['id'] as num).toInt(),
      audioFile: json['audio_file'] as String?,
      audioUrl: json['audio_url'] as String,
      imageUrl: json['image_url'] as String?,
      duration: json['duration'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      transcript: json['transcript'] as String?,
    );

Map<String, dynamic> _$AudioDataToJson(_AudioData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'audio_file': instance.audioFile,
      'audio_url': instance.audioUrl,
      'image_url': instance.imageUrl,
      'duration': instance.duration,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'transcript': instance.transcript,
    };

_VideoData _$VideoDataFromJson(Map<String, dynamic> json) => _VideoData(
      id: (json['id'] as num).toInt(),
      youtubeVideoId: json['youtube_video_id'] as String,
      thumbnailUrl: json['thumbnail_url'] as String?,
      duration: json['duration'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$VideoDataToJson(_VideoData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'youtube_video_id': instance.youtubeVideoId,
      'thumbnail_url': instance.thumbnailUrl,
      'duration': instance.duration,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

_ArticleData _$ArticleDataFromJson(Map<String, dynamic> json) => _ArticleData(
      id: (json['id'] as num).toInt(),
      text: json['text'] as String,
      imageUrl: json['image_url'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$ArticleDataToJson(_ArticleData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'text': instance.text,
      'image_url': instance.imageUrl,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

_PostData _$PostDataFromJson(Map<String, dynamic> json) => _PostData(
      id: (json['id'] as num).toInt(),
      text: json['text'] as String,
      imageUrl: json['image_url'] as String?,
      publishedAt: json['published_at'] == null
          ? null
          : DateTime.parse(json['published_at'] as String),
      url: json['url'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$PostDataToJson(_PostData instance) => <String, dynamic>{
      'id': instance.id,
      'text': instance.text,
      'image_url': instance.imageUrl,
      'published_at': instance.publishedAt?.toIso8601String(),
      'url': instance.url,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

_BookData _$BookDataFromJson(Map<String, dynamic> json) => _BookData(
      id: (json['id'] as num).toInt(),
      file: json['file'] as String?,
      pdfUrl: json['file_url'] as String?,
      coverImage: json['cover_image'] as String?,
      coverImageUrl: json['cover_image_url'] as String?,
      pages: (json['pages_count'] as num?)?.toInt(),
      publishedDate: json['published_date'] as String?,
      publisher: json['publisher'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$BookDataToJson(_BookData instance) => <String, dynamic>{
      'id': instance.id,
      'file': instance.file,
      'file_url': instance.pdfUrl,
      'cover_image': instance.coverImage,
      'cover_image_url': instance.coverImageUrl,
      'pages_count': instance.pages,
      'published_date': instance.publishedDate,
      'publisher': instance.publisher,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };
