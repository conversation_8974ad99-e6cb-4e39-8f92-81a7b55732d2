// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_data_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$FavoriteItem {
  String get id;
  String get userId;
  String get itemId;
  @JsonKey(fromJson: _mediaTypeFromJson, toJson: _mediaTypeToJson)
  MediaType get type;
  DateTime get createdAt;
  SyncStatus get syncStatus;

  /// Create a copy of FavoriteItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FavoriteItemCopyWith<FavoriteItem> get copyWith =>
      _$FavoriteItemCopyWithImpl<FavoriteItem>(
          this as FavoriteItem, _$identity);

  /// Serializes this FavoriteItem to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FavoriteItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.syncStatus, syncStatus) ||
                other.syncStatus == syncStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, userId, itemId, type, createdAt, syncStatus);

  @override
  String toString() {
    return 'FavoriteItem(id: $id, userId: $userId, itemId: $itemId, type: $type, createdAt: $createdAt, syncStatus: $syncStatus)';
  }
}

/// @nodoc
abstract mixin class $FavoriteItemCopyWith<$Res> {
  factory $FavoriteItemCopyWith(
          FavoriteItem value, $Res Function(FavoriteItem) _then) =
      _$FavoriteItemCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String userId,
      String itemId,
      @JsonKey(fromJson: _mediaTypeFromJson, toJson: _mediaTypeToJson)
      MediaType type,
      DateTime createdAt,
      SyncStatus syncStatus});
}

/// @nodoc
class _$FavoriteItemCopyWithImpl<$Res> implements $FavoriteItemCopyWith<$Res> {
  _$FavoriteItemCopyWithImpl(this._self, this._then);

  final FavoriteItem _self;
  final $Res Function(FavoriteItem) _then;

  /// Create a copy of FavoriteItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? itemId = null,
    Object? type = null,
    Object? createdAt = null,
    Object? syncStatus = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      itemId: null == itemId
          ? _self.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as MediaType,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      syncStatus: null == syncStatus
          ? _self.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as SyncStatus,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _FavoriteItem implements FavoriteItem {
  const _FavoriteItem(
      {required this.id,
      required this.userId,
      required this.itemId,
      @JsonKey(fromJson: _mediaTypeFromJson, toJson: _mediaTypeToJson)
      required this.type,
      required this.createdAt,
      this.syncStatus = SyncStatus.pending});
  factory _FavoriteItem.fromJson(Map<String, dynamic> json) =>
      _$FavoriteItemFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String itemId;
  @override
  @JsonKey(fromJson: _mediaTypeFromJson, toJson: _mediaTypeToJson)
  final MediaType type;
  @override
  final DateTime createdAt;
  @override
  @JsonKey()
  final SyncStatus syncStatus;

  /// Create a copy of FavoriteItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FavoriteItemCopyWith<_FavoriteItem> get copyWith =>
      __$FavoriteItemCopyWithImpl<_FavoriteItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FavoriteItemToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FavoriteItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.syncStatus, syncStatus) ||
                other.syncStatus == syncStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, userId, itemId, type, createdAt, syncStatus);

  @override
  String toString() {
    return 'FavoriteItem(id: $id, userId: $userId, itemId: $itemId, type: $type, createdAt: $createdAt, syncStatus: $syncStatus)';
  }
}

/// @nodoc
abstract mixin class _$FavoriteItemCopyWith<$Res>
    implements $FavoriteItemCopyWith<$Res> {
  factory _$FavoriteItemCopyWith(
          _FavoriteItem value, $Res Function(_FavoriteItem) _then) =
      __$FavoriteItemCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String itemId,
      @JsonKey(fromJson: _mediaTypeFromJson, toJson: _mediaTypeToJson)
      MediaType type,
      DateTime createdAt,
      SyncStatus syncStatus});
}

/// @nodoc
class __$FavoriteItemCopyWithImpl<$Res>
    implements _$FavoriteItemCopyWith<$Res> {
  __$FavoriteItemCopyWithImpl(this._self, this._then);

  final _FavoriteItem _self;
  final $Res Function(_FavoriteItem) _then;

  /// Create a copy of FavoriteItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? itemId = null,
    Object? type = null,
    Object? createdAt = null,
    Object? syncStatus = null,
  }) {
    return _then(_FavoriteItem(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      itemId: null == itemId
          ? _self.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as MediaType,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      syncStatus: null == syncStatus
          ? _self.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as SyncStatus,
    ));
  }
}

/// @nodoc
mixin _$BookmarkItem {
  String get id;
  String get userId;
  String get itemId;
  @JsonKey(fromJson: _mediaTypeFromJson, toJson: _mediaTypeToJson)
  MediaType get type;
  DateTime get createdAt;
  SyncStatus get syncStatus;

  /// Create a copy of BookmarkItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $BookmarkItemCopyWith<BookmarkItem> get copyWith =>
      _$BookmarkItemCopyWithImpl<BookmarkItem>(
          this as BookmarkItem, _$identity);

  /// Serializes this BookmarkItem to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is BookmarkItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.syncStatus, syncStatus) ||
                other.syncStatus == syncStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, userId, itemId, type, createdAt, syncStatus);

  @override
  String toString() {
    return 'BookmarkItem(id: $id, userId: $userId, itemId: $itemId, type: $type, createdAt: $createdAt, syncStatus: $syncStatus)';
  }
}

/// @nodoc
abstract mixin class $BookmarkItemCopyWith<$Res> {
  factory $BookmarkItemCopyWith(
          BookmarkItem value, $Res Function(BookmarkItem) _then) =
      _$BookmarkItemCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String userId,
      String itemId,
      @JsonKey(fromJson: _mediaTypeFromJson, toJson: _mediaTypeToJson)
      MediaType type,
      DateTime createdAt,
      SyncStatus syncStatus});
}

/// @nodoc
class _$BookmarkItemCopyWithImpl<$Res> implements $BookmarkItemCopyWith<$Res> {
  _$BookmarkItemCopyWithImpl(this._self, this._then);

  final BookmarkItem _self;
  final $Res Function(BookmarkItem) _then;

  /// Create a copy of BookmarkItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? itemId = null,
    Object? type = null,
    Object? createdAt = null,
    Object? syncStatus = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      itemId: null == itemId
          ? _self.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as MediaType,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      syncStatus: null == syncStatus
          ? _self.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as SyncStatus,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _BookmarkItem implements BookmarkItem {
  const _BookmarkItem(
      {required this.id,
      required this.userId,
      required this.itemId,
      @JsonKey(fromJson: _mediaTypeFromJson, toJson: _mediaTypeToJson)
      required this.type,
      required this.createdAt,
      this.syncStatus = SyncStatus.pending});
  factory _BookmarkItem.fromJson(Map<String, dynamic> json) =>
      _$BookmarkItemFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String itemId;
  @override
  @JsonKey(fromJson: _mediaTypeFromJson, toJson: _mediaTypeToJson)
  final MediaType type;
  @override
  final DateTime createdAt;
  @override
  @JsonKey()
  final SyncStatus syncStatus;

  /// Create a copy of BookmarkItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$BookmarkItemCopyWith<_BookmarkItem> get copyWith =>
      __$BookmarkItemCopyWithImpl<_BookmarkItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$BookmarkItemToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _BookmarkItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.itemId, itemId) || other.itemId == itemId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.syncStatus, syncStatus) ||
                other.syncStatus == syncStatus));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, userId, itemId, type, createdAt, syncStatus);

  @override
  String toString() {
    return 'BookmarkItem(id: $id, userId: $userId, itemId: $itemId, type: $type, createdAt: $createdAt, syncStatus: $syncStatus)';
  }
}

/// @nodoc
abstract mixin class _$BookmarkItemCopyWith<$Res>
    implements $BookmarkItemCopyWith<$Res> {
  factory _$BookmarkItemCopyWith(
          _BookmarkItem value, $Res Function(_BookmarkItem) _then) =
      __$BookmarkItemCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String itemId,
      @JsonKey(fromJson: _mediaTypeFromJson, toJson: _mediaTypeToJson)
      MediaType type,
      DateTime createdAt,
      SyncStatus syncStatus});
}

/// @nodoc
class __$BookmarkItemCopyWithImpl<$Res>
    implements _$BookmarkItemCopyWith<$Res> {
  __$BookmarkItemCopyWithImpl(this._self, this._then);

  final _BookmarkItem _self;
  final $Res Function(_BookmarkItem) _then;

  /// Create a copy of BookmarkItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? itemId = null,
    Object? type = null,
    Object? createdAt = null,
    Object? syncStatus = null,
  }) {
    return _then(_BookmarkItem(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      itemId: null == itemId
          ? _self.itemId
          : itemId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as MediaType,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      syncStatus: null == syncStatus
          ? _self.syncStatus
          : syncStatus // ignore: cast_nullable_to_non_nullable
              as SyncStatus,
    ));
  }
}

// dart format on
