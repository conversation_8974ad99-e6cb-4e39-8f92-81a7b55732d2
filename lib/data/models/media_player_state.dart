import 'package:freezed_annotation/freezed_annotation.dart';

import 'content_model.dart';

part 'media_player_state.freezed.dart';

@freezed
abstract class MediaPlayerState with _$MediaPlayerState {
  const factory MediaPlayerState({
    required ContentModel? mediaItem,
    String? errorMessage,
    @Default(false) bool isDownloading,
    double? downloadProgress,
    String? downloadedFilePath,
  }) = _MediaPlayerState;
}
