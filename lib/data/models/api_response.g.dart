// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'api_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CategoryContentData _$CategoryContentDataFromJson(Map<String, dynamic> json) =>
    _CategoryContentData(
      category:
          CategoryModel.fromJson(json['category'] as Map<String, dynamic>),
      contents: (json['contents'] as List<dynamic>)
          .map((e) => ContentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      pagination:
          PaginationInfo.fromJson(json['pagination'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CategoryContentDataToJson(
        _CategoryContentData instance) =>
    <String, dynamic>{
      'category': instance.category,
      'contents': instance.contents,
      'pagination': instance.pagination,
    };

_PaginatedContentResponse _$PaginatedContentResponseFromJson(
        Map<String, dynamic> json) =>
    _PaginatedContentResponse(
      data: CategoryContentData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PaginatedContentResponseToJson(
        _PaginatedContentResponse instance) =>
    <String, dynamic>{
      'data': instance.data,
    };

_PaginationInfo _$PaginationInfoFromJson(Map<String, dynamic> json) =>
    _PaginationInfo(
      currentPage: (json['current_page'] as num).toInt(),
      perPage: (json['per_page'] as num).toInt(),
      total: (json['total'] as num).toInt(),
      lastPage: (json['last_page'] as num).toInt(),
      from: (json['from'] as num?)?.toInt(),
      to: (json['to'] as num?)?.toInt(),
      hasMore: json['has_more'] as bool,
    );

Map<String, dynamic> _$PaginationInfoToJson(_PaginationInfo instance) =>
    <String, dynamic>{
      'current_page': instance.currentPage,
      'per_page': instance.perPage,
      'total': instance.total,
      'last_page': instance.lastPage,
      'from': instance.from,
      'to': instance.to,
      'has_more': instance.hasMore,
    };

_SearchResultsResponse _$SearchResultsResponseFromJson(
        Map<String, dynamic> json) =>
    _SearchResultsResponse(
      searchQuery: json['search_query'] as String?,
      totalResults: (json['total_results'] as num).toInt(),
      categoriesWithResults: (json['categories_with_results'] as num).toInt(),
      results: (json['results'] as List<dynamic>)
          .map((e) => CategorySearchResult.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$SearchResultsResponseToJson(
        _SearchResultsResponse instance) =>
    <String, dynamic>{
      'search_query': instance.searchQuery,
      'total_results': instance.totalResults,
      'categories_with_results': instance.categoriesWithResults,
      'results': instance.results,
    };

_CategorySearchResult _$CategorySearchResultFromJson(
        Map<String, dynamic> json) =>
    _CategorySearchResult(
      category:
          CategoryModel.fromJson(json['category'] as Map<String, dynamic>),
      contents: (json['contents'] as List<dynamic>)
          .map((e) => ContentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      contentCount: (json['content_count'] as num).toInt(),
    );

Map<String, dynamic> _$CategorySearchResultToJson(
        _CategorySearchResult instance) =>
    <String, dynamic>{
      'category': instance.category,
      'contents': instance.contents,
      'content_count': instance.contentCount,
    };
