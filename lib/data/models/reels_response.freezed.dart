// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'reels_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ReelsData {
  CategoryModel get category;
  List<ContentModel> get contents;

  /// Create a copy of ReelsData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ReelsDataCopyWith<ReelsData> get copyWith =>
      _$ReelsDataCopyWithImpl<ReelsData>(this as ReelsData, _$identity);

  /// Serializes this ReelsData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ReelsData &&
            (identical(other.category, category) ||
                other.category == category) &&
            const DeepCollectionEquality().equals(other.contents, contents));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, category, const DeepCollectionEquality().hash(contents));

  @override
  String toString() {
    return 'ReelsData(category: $category, contents: $contents)';
  }
}

/// @nodoc
abstract mixin class $ReelsDataCopyWith<$Res> {
  factory $ReelsDataCopyWith(ReelsData value, $Res Function(ReelsData) _then) =
      _$ReelsDataCopyWithImpl;
  @useResult
  $Res call({CategoryModel category, List<ContentModel> contents});

  $CategoryModelCopyWith<$Res> get category;
}

/// @nodoc
class _$ReelsDataCopyWithImpl<$Res> implements $ReelsDataCopyWith<$Res> {
  _$ReelsDataCopyWithImpl(this._self, this._then);

  final ReelsData _self;
  final $Res Function(ReelsData) _then;

  /// Create a copy of ReelsData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? category = null,
    Object? contents = null,
  }) {
    return _then(_self.copyWith(
      category: null == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as CategoryModel,
      contents: null == contents
          ? _self.contents
          : contents // ignore: cast_nullable_to_non_nullable
              as List<ContentModel>,
    ));
  }

  /// Create a copy of ReelsData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CategoryModelCopyWith<$Res> get category {
    return $CategoryModelCopyWith<$Res>(_self.category, (value) {
      return _then(_self.copyWith(category: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _ReelsData implements ReelsData {
  const _ReelsData(
      {required this.category, required final List<ContentModel> contents})
      : _contents = contents;
  factory _ReelsData.fromJson(Map<String, dynamic> json) =>
      _$ReelsDataFromJson(json);

  @override
  final CategoryModel category;
  final List<ContentModel> _contents;
  @override
  List<ContentModel> get contents {
    if (_contents is EqualUnmodifiableListView) return _contents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contents);
  }

  /// Create a copy of ReelsData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ReelsDataCopyWith<_ReelsData> get copyWith =>
      __$ReelsDataCopyWithImpl<_ReelsData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ReelsDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ReelsData &&
            (identical(other.category, category) ||
                other.category == category) &&
            const DeepCollectionEquality().equals(other._contents, _contents));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, category, const DeepCollectionEquality().hash(_contents));

  @override
  String toString() {
    return 'ReelsData(category: $category, contents: $contents)';
  }
}

/// @nodoc
abstract mixin class _$ReelsDataCopyWith<$Res>
    implements $ReelsDataCopyWith<$Res> {
  factory _$ReelsDataCopyWith(
          _ReelsData value, $Res Function(_ReelsData) _then) =
      __$ReelsDataCopyWithImpl;
  @override
  @useResult
  $Res call({CategoryModel category, List<ContentModel> contents});

  @override
  $CategoryModelCopyWith<$Res> get category;
}

/// @nodoc
class __$ReelsDataCopyWithImpl<$Res> implements _$ReelsDataCopyWith<$Res> {
  __$ReelsDataCopyWithImpl(this._self, this._then);

  final _ReelsData _self;
  final $Res Function(_ReelsData) _then;

  /// Create a copy of ReelsData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? category = null,
    Object? contents = null,
  }) {
    return _then(_ReelsData(
      category: null == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as CategoryModel,
      contents: null == contents
          ? _self._contents
          : contents // ignore: cast_nullable_to_non_nullable
              as List<ContentModel>,
    ));
  }

  /// Create a copy of ReelsData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CategoryModelCopyWith<$Res> get category {
    return $CategoryModelCopyWith<$Res>(_self.category, (value) {
      return _then(_self.copyWith(category: value));
    });
  }
}

/// @nodoc
mixin _$ReelsContentResponse {
  ReelsData get data;

  /// Create a copy of ReelsContentResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ReelsContentResponseCopyWith<ReelsContentResponse> get copyWith =>
      _$ReelsContentResponseCopyWithImpl<ReelsContentResponse>(
          this as ReelsContentResponse, _$identity);

  /// Serializes this ReelsContentResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ReelsContentResponse &&
            (identical(other.data, data) || other.data == data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, data);

  @override
  String toString() {
    return 'ReelsContentResponse(data: $data)';
  }
}

/// @nodoc
abstract mixin class $ReelsContentResponseCopyWith<$Res> {
  factory $ReelsContentResponseCopyWith(ReelsContentResponse value,
          $Res Function(ReelsContentResponse) _then) =
      _$ReelsContentResponseCopyWithImpl;
  @useResult
  $Res call({ReelsData data});

  $ReelsDataCopyWith<$Res> get data;
}

/// @nodoc
class _$ReelsContentResponseCopyWithImpl<$Res>
    implements $ReelsContentResponseCopyWith<$Res> {
  _$ReelsContentResponseCopyWithImpl(this._self, this._then);

  final ReelsContentResponse _self;
  final $Res Function(ReelsContentResponse) _then;

  /// Create a copy of ReelsContentResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_self.copyWith(
      data: null == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as ReelsData,
    ));
  }

  /// Create a copy of ReelsContentResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ReelsDataCopyWith<$Res> get data {
    return $ReelsDataCopyWith<$Res>(_self.data, (value) {
      return _then(_self.copyWith(data: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _ReelsContentResponse implements ReelsContentResponse {
  const _ReelsContentResponse({required this.data});
  factory _ReelsContentResponse.fromJson(Map<String, dynamic> json) =>
      _$ReelsContentResponseFromJson(json);

  @override
  final ReelsData data;

  /// Create a copy of ReelsContentResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ReelsContentResponseCopyWith<_ReelsContentResponse> get copyWith =>
      __$ReelsContentResponseCopyWithImpl<_ReelsContentResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ReelsContentResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ReelsContentResponse &&
            (identical(other.data, data) || other.data == data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, data);

  @override
  String toString() {
    return 'ReelsContentResponse(data: $data)';
  }
}

/// @nodoc
abstract mixin class _$ReelsContentResponseCopyWith<$Res>
    implements $ReelsContentResponseCopyWith<$Res> {
  factory _$ReelsContentResponseCopyWith(_ReelsContentResponse value,
          $Res Function(_ReelsContentResponse) _then) =
      __$ReelsContentResponseCopyWithImpl;
  @override
  @useResult
  $Res call({ReelsData data});

  @override
  $ReelsDataCopyWith<$Res> get data;
}

/// @nodoc
class __$ReelsContentResponseCopyWithImpl<$Res>
    implements _$ReelsContentResponseCopyWith<$Res> {
  __$ReelsContentResponseCopyWithImpl(this._self, this._then);

  final _ReelsContentResponse _self;
  final $Res Function(_ReelsContentResponse) _then;

  /// Create a copy of ReelsContentResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? data = null,
  }) {
    return _then(_ReelsContentResponse(
      data: null == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as ReelsData,
    ));
  }

  /// Create a copy of ReelsContentResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ReelsDataCopyWith<$Res> get data {
    return $ReelsDataCopyWith<$Res>(_self.data, (value) {
      return _then(_self.copyWith(data: value));
    });
  }
}

// dart format on
