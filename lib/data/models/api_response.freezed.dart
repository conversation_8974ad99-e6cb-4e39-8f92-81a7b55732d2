// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'api_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CategoryContentData {
  CategoryModel get category;
  List<ContentModel> get contents;
  PaginationInfo get pagination;

  /// Create a copy of CategoryContentData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CategoryContentDataCopyWith<CategoryContentData> get copyWith =>
      _$CategoryContentDataCopyWithImpl<CategoryContentData>(
          this as CategoryContentData, _$identity);

  /// Serializes this CategoryContentData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CategoryContentData &&
            (identical(other.category, category) ||
                other.category == category) &&
            const DeepCollectionEquality().equals(other.contents, contents) &&
            (identical(other.pagination, pagination) ||
                other.pagination == pagination));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, category,
      const DeepCollectionEquality().hash(contents), pagination);

  @override
  String toString() {
    return 'CategoryContentData(category: $category, contents: $contents, pagination: $pagination)';
  }
}

/// @nodoc
abstract mixin class $CategoryContentDataCopyWith<$Res> {
  factory $CategoryContentDataCopyWith(
          CategoryContentData value, $Res Function(CategoryContentData) _then) =
      _$CategoryContentDataCopyWithImpl;
  @useResult
  $Res call(
      {CategoryModel category,
      List<ContentModel> contents,
      PaginationInfo pagination});

  $CategoryModelCopyWith<$Res> get category;
  $PaginationInfoCopyWith<$Res> get pagination;
}

/// @nodoc
class _$CategoryContentDataCopyWithImpl<$Res>
    implements $CategoryContentDataCopyWith<$Res> {
  _$CategoryContentDataCopyWithImpl(this._self, this._then);

  final CategoryContentData _self;
  final $Res Function(CategoryContentData) _then;

  /// Create a copy of CategoryContentData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? category = null,
    Object? contents = null,
    Object? pagination = null,
  }) {
    return _then(_self.copyWith(
      category: null == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as CategoryModel,
      contents: null == contents
          ? _self.contents
          : contents // ignore: cast_nullable_to_non_nullable
              as List<ContentModel>,
      pagination: null == pagination
          ? _self.pagination
          : pagination // ignore: cast_nullable_to_non_nullable
              as PaginationInfo,
    ));
  }

  /// Create a copy of CategoryContentData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CategoryModelCopyWith<$Res> get category {
    return $CategoryModelCopyWith<$Res>(_self.category, (value) {
      return _then(_self.copyWith(category: value));
    });
  }

  /// Create a copy of CategoryContentData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaginationInfoCopyWith<$Res> get pagination {
    return $PaginationInfoCopyWith<$Res>(_self.pagination, (value) {
      return _then(_self.copyWith(pagination: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _CategoryContentData implements CategoryContentData {
  const _CategoryContentData(
      {required this.category,
      required final List<ContentModel> contents,
      required this.pagination})
      : _contents = contents;
  factory _CategoryContentData.fromJson(Map<String, dynamic> json) =>
      _$CategoryContentDataFromJson(json);

  @override
  final CategoryModel category;
  final List<ContentModel> _contents;
  @override
  List<ContentModel> get contents {
    if (_contents is EqualUnmodifiableListView) return _contents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contents);
  }

  @override
  final PaginationInfo pagination;

  /// Create a copy of CategoryContentData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CategoryContentDataCopyWith<_CategoryContentData> get copyWith =>
      __$CategoryContentDataCopyWithImpl<_CategoryContentData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CategoryContentDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CategoryContentData &&
            (identical(other.category, category) ||
                other.category == category) &&
            const DeepCollectionEquality().equals(other._contents, _contents) &&
            (identical(other.pagination, pagination) ||
                other.pagination == pagination));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, category,
      const DeepCollectionEquality().hash(_contents), pagination);

  @override
  String toString() {
    return 'CategoryContentData(category: $category, contents: $contents, pagination: $pagination)';
  }
}

/// @nodoc
abstract mixin class _$CategoryContentDataCopyWith<$Res>
    implements $CategoryContentDataCopyWith<$Res> {
  factory _$CategoryContentDataCopyWith(_CategoryContentData value,
          $Res Function(_CategoryContentData) _then) =
      __$CategoryContentDataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {CategoryModel category,
      List<ContentModel> contents,
      PaginationInfo pagination});

  @override
  $CategoryModelCopyWith<$Res> get category;
  @override
  $PaginationInfoCopyWith<$Res> get pagination;
}

/// @nodoc
class __$CategoryContentDataCopyWithImpl<$Res>
    implements _$CategoryContentDataCopyWith<$Res> {
  __$CategoryContentDataCopyWithImpl(this._self, this._then);

  final _CategoryContentData _self;
  final $Res Function(_CategoryContentData) _then;

  /// Create a copy of CategoryContentData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? category = null,
    Object? contents = null,
    Object? pagination = null,
  }) {
    return _then(_CategoryContentData(
      category: null == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as CategoryModel,
      contents: null == contents
          ? _self._contents
          : contents // ignore: cast_nullable_to_non_nullable
              as List<ContentModel>,
      pagination: null == pagination
          ? _self.pagination
          : pagination // ignore: cast_nullable_to_non_nullable
              as PaginationInfo,
    ));
  }

  /// Create a copy of CategoryContentData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CategoryModelCopyWith<$Res> get category {
    return $CategoryModelCopyWith<$Res>(_self.category, (value) {
      return _then(_self.copyWith(category: value));
    });
  }

  /// Create a copy of CategoryContentData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PaginationInfoCopyWith<$Res> get pagination {
    return $PaginationInfoCopyWith<$Res>(_self.pagination, (value) {
      return _then(_self.copyWith(pagination: value));
    });
  }
}

/// @nodoc
mixin _$PaginatedContentResponse {
  CategoryContentData get data;

  /// Create a copy of PaginatedContentResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PaginatedContentResponseCopyWith<PaginatedContentResponse> get copyWith =>
      _$PaginatedContentResponseCopyWithImpl<PaginatedContentResponse>(
          this as PaginatedContentResponse, _$identity);

  /// Serializes this PaginatedContentResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PaginatedContentResponse &&
            (identical(other.data, data) || other.data == data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, data);

  @override
  String toString() {
    return 'PaginatedContentResponse(data: $data)';
  }
}

/// @nodoc
abstract mixin class $PaginatedContentResponseCopyWith<$Res> {
  factory $PaginatedContentResponseCopyWith(PaginatedContentResponse value,
          $Res Function(PaginatedContentResponse) _then) =
      _$PaginatedContentResponseCopyWithImpl;
  @useResult
  $Res call({CategoryContentData data});

  $CategoryContentDataCopyWith<$Res> get data;
}

/// @nodoc
class _$PaginatedContentResponseCopyWithImpl<$Res>
    implements $PaginatedContentResponseCopyWith<$Res> {
  _$PaginatedContentResponseCopyWithImpl(this._self, this._then);

  final PaginatedContentResponse _self;
  final $Res Function(PaginatedContentResponse) _then;

  /// Create a copy of PaginatedContentResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_self.copyWith(
      data: null == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as CategoryContentData,
    ));
  }

  /// Create a copy of PaginatedContentResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CategoryContentDataCopyWith<$Res> get data {
    return $CategoryContentDataCopyWith<$Res>(_self.data, (value) {
      return _then(_self.copyWith(data: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _PaginatedContentResponse implements PaginatedContentResponse {
  const _PaginatedContentResponse({required this.data});
  factory _PaginatedContentResponse.fromJson(Map<String, dynamic> json) =>
      _$PaginatedContentResponseFromJson(json);

  @override
  final CategoryContentData data;

  /// Create a copy of PaginatedContentResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PaginatedContentResponseCopyWith<_PaginatedContentResponse> get copyWith =>
      __$PaginatedContentResponseCopyWithImpl<_PaginatedContentResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PaginatedContentResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PaginatedContentResponse &&
            (identical(other.data, data) || other.data == data));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, data);

  @override
  String toString() {
    return 'PaginatedContentResponse(data: $data)';
  }
}

/// @nodoc
abstract mixin class _$PaginatedContentResponseCopyWith<$Res>
    implements $PaginatedContentResponseCopyWith<$Res> {
  factory _$PaginatedContentResponseCopyWith(_PaginatedContentResponse value,
          $Res Function(_PaginatedContentResponse) _then) =
      __$PaginatedContentResponseCopyWithImpl;
  @override
  @useResult
  $Res call({CategoryContentData data});

  @override
  $CategoryContentDataCopyWith<$Res> get data;
}

/// @nodoc
class __$PaginatedContentResponseCopyWithImpl<$Res>
    implements _$PaginatedContentResponseCopyWith<$Res> {
  __$PaginatedContentResponseCopyWithImpl(this._self, this._then);

  final _PaginatedContentResponse _self;
  final $Res Function(_PaginatedContentResponse) _then;

  /// Create a copy of PaginatedContentResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? data = null,
  }) {
    return _then(_PaginatedContentResponse(
      data: null == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as CategoryContentData,
    ));
  }

  /// Create a copy of PaginatedContentResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CategoryContentDataCopyWith<$Res> get data {
    return $CategoryContentDataCopyWith<$Res>(_self.data, (value) {
      return _then(_self.copyWith(data: value));
    });
  }
}

/// @nodoc
mixin _$PaginationInfo {
  @JsonKey(name: 'current_page')
  int get currentPage;
  @JsonKey(name: 'per_page')
  int get perPage;
  int get total;
  @JsonKey(name: 'last_page')
  int get lastPage;
  int? get from;
  int? get to;
  @JsonKey(name: 'has_more')
  bool get hasMore;

  /// Create a copy of PaginationInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PaginationInfoCopyWith<PaginationInfo> get copyWith =>
      _$PaginationInfoCopyWithImpl<PaginationInfo>(
          this as PaginationInfo, _$identity);

  /// Serializes this PaginationInfo to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PaginationInfo &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.perPage, perPage) || other.perPage == perPage) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.lastPage, lastPage) ||
                other.lastPage == lastPage) &&
            (identical(other.from, from) || other.from == from) &&
            (identical(other.to, to) || other.to == to) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, currentPage, perPage, total, lastPage, from, to, hasMore);

  @override
  String toString() {
    return 'PaginationInfo(currentPage: $currentPage, perPage: $perPage, total: $total, lastPage: $lastPage, from: $from, to: $to, hasMore: $hasMore)';
  }
}

/// @nodoc
abstract mixin class $PaginationInfoCopyWith<$Res> {
  factory $PaginationInfoCopyWith(
          PaginationInfo value, $Res Function(PaginationInfo) _then) =
      _$PaginationInfoCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'current_page') int currentPage,
      @JsonKey(name: 'per_page') int perPage,
      int total,
      @JsonKey(name: 'last_page') int lastPage,
      int? from,
      int? to,
      @JsonKey(name: 'has_more') bool hasMore});
}

/// @nodoc
class _$PaginationInfoCopyWithImpl<$Res>
    implements $PaginationInfoCopyWith<$Res> {
  _$PaginationInfoCopyWithImpl(this._self, this._then);

  final PaginationInfo _self;
  final $Res Function(PaginationInfo) _then;

  /// Create a copy of PaginationInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? currentPage = null,
    Object? perPage = null,
    Object? total = null,
    Object? lastPage = null,
    Object? from = freezed,
    Object? to = freezed,
    Object? hasMore = null,
  }) {
    return _then(_self.copyWith(
      currentPage: null == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      perPage: null == perPage
          ? _self.perPage
          : perPage // ignore: cast_nullable_to_non_nullable
              as int,
      total: null == total
          ? _self.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      lastPage: null == lastPage
          ? _self.lastPage
          : lastPage // ignore: cast_nullable_to_non_nullable
              as int,
      from: freezed == from
          ? _self.from
          : from // ignore: cast_nullable_to_non_nullable
              as int?,
      to: freezed == to
          ? _self.to
          : to // ignore: cast_nullable_to_non_nullable
              as int?,
      hasMore: null == hasMore
          ? _self.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _PaginationInfo implements PaginationInfo {
  const _PaginationInfo(
      {@JsonKey(name: 'current_page') required this.currentPage,
      @JsonKey(name: 'per_page') required this.perPage,
      required this.total,
      @JsonKey(name: 'last_page') required this.lastPage,
      this.from,
      this.to,
      @JsonKey(name: 'has_more') required this.hasMore});
  factory _PaginationInfo.fromJson(Map<String, dynamic> json) =>
      _$PaginationInfoFromJson(json);

  @override
  @JsonKey(name: 'current_page')
  final int currentPage;
  @override
  @JsonKey(name: 'per_page')
  final int perPage;
  @override
  final int total;
  @override
  @JsonKey(name: 'last_page')
  final int lastPage;
  @override
  final int? from;
  @override
  final int? to;
  @override
  @JsonKey(name: 'has_more')
  final bool hasMore;

  /// Create a copy of PaginationInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PaginationInfoCopyWith<_PaginationInfo> get copyWith =>
      __$PaginationInfoCopyWithImpl<_PaginationInfo>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PaginationInfoToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PaginationInfo &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.perPage, perPage) || other.perPage == perPage) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.lastPage, lastPage) ||
                other.lastPage == lastPage) &&
            (identical(other.from, from) || other.from == from) &&
            (identical(other.to, to) || other.to == to) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, currentPage, perPage, total, lastPage, from, to, hasMore);

  @override
  String toString() {
    return 'PaginationInfo(currentPage: $currentPage, perPage: $perPage, total: $total, lastPage: $lastPage, from: $from, to: $to, hasMore: $hasMore)';
  }
}

/// @nodoc
abstract mixin class _$PaginationInfoCopyWith<$Res>
    implements $PaginationInfoCopyWith<$Res> {
  factory _$PaginationInfoCopyWith(
          _PaginationInfo value, $Res Function(_PaginationInfo) _then) =
      __$PaginationInfoCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'current_page') int currentPage,
      @JsonKey(name: 'per_page') int perPage,
      int total,
      @JsonKey(name: 'last_page') int lastPage,
      int? from,
      int? to,
      @JsonKey(name: 'has_more') bool hasMore});
}

/// @nodoc
class __$PaginationInfoCopyWithImpl<$Res>
    implements _$PaginationInfoCopyWith<$Res> {
  __$PaginationInfoCopyWithImpl(this._self, this._then);

  final _PaginationInfo _self;
  final $Res Function(_PaginationInfo) _then;

  /// Create a copy of PaginationInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? currentPage = null,
    Object? perPage = null,
    Object? total = null,
    Object? lastPage = null,
    Object? from = freezed,
    Object? to = freezed,
    Object? hasMore = null,
  }) {
    return _then(_PaginationInfo(
      currentPage: null == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      perPage: null == perPage
          ? _self.perPage
          : perPage // ignore: cast_nullable_to_non_nullable
              as int,
      total: null == total
          ? _self.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      lastPage: null == lastPage
          ? _self.lastPage
          : lastPage // ignore: cast_nullable_to_non_nullable
              as int,
      from: freezed == from
          ? _self.from
          : from // ignore: cast_nullable_to_non_nullable
              as int?,
      to: freezed == to
          ? _self.to
          : to // ignore: cast_nullable_to_non_nullable
              as int?,
      hasMore: null == hasMore
          ? _self.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
mixin _$SearchResultsResponse {
  @JsonKey(name: 'search_query')
  String? get searchQuery;
  @JsonKey(name: 'total_results')
  int get totalResults;
  @JsonKey(name: 'categories_with_results')
  int get categoriesWithResults;
  List<CategorySearchResult> get results;

  /// Create a copy of SearchResultsResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SearchResultsResponseCopyWith<SearchResultsResponse> get copyWith =>
      _$SearchResultsResponseCopyWithImpl<SearchResultsResponse>(
          this as SearchResultsResponse, _$identity);

  /// Serializes this SearchResultsResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SearchResultsResponse &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery) &&
            (identical(other.totalResults, totalResults) ||
                other.totalResults == totalResults) &&
            (identical(other.categoriesWithResults, categoriesWithResults) ||
                other.categoriesWithResults == categoriesWithResults) &&
            const DeepCollectionEquality().equals(other.results, results));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, searchQuery, totalResults,
      categoriesWithResults, const DeepCollectionEquality().hash(results));

  @override
  String toString() {
    return 'SearchResultsResponse(searchQuery: $searchQuery, totalResults: $totalResults, categoriesWithResults: $categoriesWithResults, results: $results)';
  }
}

/// @nodoc
abstract mixin class $SearchResultsResponseCopyWith<$Res> {
  factory $SearchResultsResponseCopyWith(SearchResultsResponse value,
          $Res Function(SearchResultsResponse) _then) =
      _$SearchResultsResponseCopyWithImpl;
  @useResult
  $Res call(
      {@JsonKey(name: 'search_query') String? searchQuery,
      @JsonKey(name: 'total_results') int totalResults,
      @JsonKey(name: 'categories_with_results') int categoriesWithResults,
      List<CategorySearchResult> results});
}

/// @nodoc
class _$SearchResultsResponseCopyWithImpl<$Res>
    implements $SearchResultsResponseCopyWith<$Res> {
  _$SearchResultsResponseCopyWithImpl(this._self, this._then);

  final SearchResultsResponse _self;
  final $Res Function(SearchResultsResponse) _then;

  /// Create a copy of SearchResultsResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchQuery = freezed,
    Object? totalResults = null,
    Object? categoriesWithResults = null,
    Object? results = null,
  }) {
    return _then(_self.copyWith(
      searchQuery: freezed == searchQuery
          ? _self.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String?,
      totalResults: null == totalResults
          ? _self.totalResults
          : totalResults // ignore: cast_nullable_to_non_nullable
              as int,
      categoriesWithResults: null == categoriesWithResults
          ? _self.categoriesWithResults
          : categoriesWithResults // ignore: cast_nullable_to_non_nullable
              as int,
      results: null == results
          ? _self.results
          : results // ignore: cast_nullable_to_non_nullable
              as List<CategorySearchResult>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _SearchResultsResponse implements SearchResultsResponse {
  const _SearchResultsResponse(
      {@JsonKey(name: 'search_query') this.searchQuery,
      @JsonKey(name: 'total_results') required this.totalResults,
      @JsonKey(name: 'categories_with_results')
      required this.categoriesWithResults,
      required final List<CategorySearchResult> results})
      : _results = results;
  factory _SearchResultsResponse.fromJson(Map<String, dynamic> json) =>
      _$SearchResultsResponseFromJson(json);

  @override
  @JsonKey(name: 'search_query')
  final String? searchQuery;
  @override
  @JsonKey(name: 'total_results')
  final int totalResults;
  @override
  @JsonKey(name: 'categories_with_results')
  final int categoriesWithResults;
  final List<CategorySearchResult> _results;
  @override
  List<CategorySearchResult> get results {
    if (_results is EqualUnmodifiableListView) return _results;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_results);
  }

  /// Create a copy of SearchResultsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SearchResultsResponseCopyWith<_SearchResultsResponse> get copyWith =>
      __$SearchResultsResponseCopyWithImpl<_SearchResultsResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SearchResultsResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SearchResultsResponse &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery) &&
            (identical(other.totalResults, totalResults) ||
                other.totalResults == totalResults) &&
            (identical(other.categoriesWithResults, categoriesWithResults) ||
                other.categoriesWithResults == categoriesWithResults) &&
            const DeepCollectionEquality().equals(other._results, _results));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, searchQuery, totalResults,
      categoriesWithResults, const DeepCollectionEquality().hash(_results));

  @override
  String toString() {
    return 'SearchResultsResponse(searchQuery: $searchQuery, totalResults: $totalResults, categoriesWithResults: $categoriesWithResults, results: $results)';
  }
}

/// @nodoc
abstract mixin class _$SearchResultsResponseCopyWith<$Res>
    implements $SearchResultsResponseCopyWith<$Res> {
  factory _$SearchResultsResponseCopyWith(_SearchResultsResponse value,
          $Res Function(_SearchResultsResponse) _then) =
      __$SearchResultsResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'search_query') String? searchQuery,
      @JsonKey(name: 'total_results') int totalResults,
      @JsonKey(name: 'categories_with_results') int categoriesWithResults,
      List<CategorySearchResult> results});
}

/// @nodoc
class __$SearchResultsResponseCopyWithImpl<$Res>
    implements _$SearchResultsResponseCopyWith<$Res> {
  __$SearchResultsResponseCopyWithImpl(this._self, this._then);

  final _SearchResultsResponse _self;
  final $Res Function(_SearchResultsResponse) _then;

  /// Create a copy of SearchResultsResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? searchQuery = freezed,
    Object? totalResults = null,
    Object? categoriesWithResults = null,
    Object? results = null,
  }) {
    return _then(_SearchResultsResponse(
      searchQuery: freezed == searchQuery
          ? _self.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String?,
      totalResults: null == totalResults
          ? _self.totalResults
          : totalResults // ignore: cast_nullable_to_non_nullable
              as int,
      categoriesWithResults: null == categoriesWithResults
          ? _self.categoriesWithResults
          : categoriesWithResults // ignore: cast_nullable_to_non_nullable
              as int,
      results: null == results
          ? _self._results
          : results // ignore: cast_nullable_to_non_nullable
              as List<CategorySearchResult>,
    ));
  }
}

/// @nodoc
mixin _$CategorySearchResult {
  CategoryModel get category;
  List<ContentModel> get contents;
  @JsonKey(name: 'content_count')
  int get contentCount;

  /// Create a copy of CategorySearchResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CategorySearchResultCopyWith<CategorySearchResult> get copyWith =>
      _$CategorySearchResultCopyWithImpl<CategorySearchResult>(
          this as CategorySearchResult, _$identity);

  /// Serializes this CategorySearchResult to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CategorySearchResult &&
            (identical(other.category, category) ||
                other.category == category) &&
            const DeepCollectionEquality().equals(other.contents, contents) &&
            (identical(other.contentCount, contentCount) ||
                other.contentCount == contentCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, category,
      const DeepCollectionEquality().hash(contents), contentCount);

  @override
  String toString() {
    return 'CategorySearchResult(category: $category, contents: $contents, contentCount: $contentCount)';
  }
}

/// @nodoc
abstract mixin class $CategorySearchResultCopyWith<$Res> {
  factory $CategorySearchResultCopyWith(CategorySearchResult value,
          $Res Function(CategorySearchResult) _then) =
      _$CategorySearchResultCopyWithImpl;
  @useResult
  $Res call(
      {CategoryModel category,
      List<ContentModel> contents,
      @JsonKey(name: 'content_count') int contentCount});

  $CategoryModelCopyWith<$Res> get category;
}

/// @nodoc
class _$CategorySearchResultCopyWithImpl<$Res>
    implements $CategorySearchResultCopyWith<$Res> {
  _$CategorySearchResultCopyWithImpl(this._self, this._then);

  final CategorySearchResult _self;
  final $Res Function(CategorySearchResult) _then;

  /// Create a copy of CategorySearchResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? category = null,
    Object? contents = null,
    Object? contentCount = null,
  }) {
    return _then(_self.copyWith(
      category: null == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as CategoryModel,
      contents: null == contents
          ? _self.contents
          : contents // ignore: cast_nullable_to_non_nullable
              as List<ContentModel>,
      contentCount: null == contentCount
          ? _self.contentCount
          : contentCount // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }

  /// Create a copy of CategorySearchResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CategoryModelCopyWith<$Res> get category {
    return $CategoryModelCopyWith<$Res>(_self.category, (value) {
      return _then(_self.copyWith(category: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _CategorySearchResult implements CategorySearchResult {
  const _CategorySearchResult(
      {required this.category,
      required final List<ContentModel> contents,
      @JsonKey(name: 'content_count') required this.contentCount})
      : _contents = contents;
  factory _CategorySearchResult.fromJson(Map<String, dynamic> json) =>
      _$CategorySearchResultFromJson(json);

  @override
  final CategoryModel category;
  final List<ContentModel> _contents;
  @override
  List<ContentModel> get contents {
    if (_contents is EqualUnmodifiableListView) return _contents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_contents);
  }

  @override
  @JsonKey(name: 'content_count')
  final int contentCount;

  /// Create a copy of CategorySearchResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CategorySearchResultCopyWith<_CategorySearchResult> get copyWith =>
      __$CategorySearchResultCopyWithImpl<_CategorySearchResult>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CategorySearchResultToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CategorySearchResult &&
            (identical(other.category, category) ||
                other.category == category) &&
            const DeepCollectionEquality().equals(other._contents, _contents) &&
            (identical(other.contentCount, contentCount) ||
                other.contentCount == contentCount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, category,
      const DeepCollectionEquality().hash(_contents), contentCount);

  @override
  String toString() {
    return 'CategorySearchResult(category: $category, contents: $contents, contentCount: $contentCount)';
  }
}

/// @nodoc
abstract mixin class _$CategorySearchResultCopyWith<$Res>
    implements $CategorySearchResultCopyWith<$Res> {
  factory _$CategorySearchResultCopyWith(_CategorySearchResult value,
          $Res Function(_CategorySearchResult) _then) =
      __$CategorySearchResultCopyWithImpl;
  @override
  @useResult
  $Res call(
      {CategoryModel category,
      List<ContentModel> contents,
      @JsonKey(name: 'content_count') int contentCount});

  @override
  $CategoryModelCopyWith<$Res> get category;
}

/// @nodoc
class __$CategorySearchResultCopyWithImpl<$Res>
    implements _$CategorySearchResultCopyWith<$Res> {
  __$CategorySearchResultCopyWithImpl(this._self, this._then);

  final _CategorySearchResult _self;
  final $Res Function(_CategorySearchResult) _then;

  /// Create a copy of CategorySearchResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? category = null,
    Object? contents = null,
    Object? contentCount = null,
  }) {
    return _then(_CategorySearchResult(
      category: null == category
          ? _self.category
          : category // ignore: cast_nullable_to_non_nullable
              as CategoryModel,
      contents: null == contents
          ? _self._contents
          : contents // ignore: cast_nullable_to_non_nullable
              as List<ContentModel>,
      contentCount: null == contentCount
          ? _self.contentCount
          : contentCount // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }

  /// Create a copy of CategorySearchResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CategoryModelCopyWith<$Res> get category {
    return $CategoryModelCopyWith<$Res>(_self.category, (value) {
      return _then(_self.copyWith(category: value));
    });
  }
}

// dart format on
