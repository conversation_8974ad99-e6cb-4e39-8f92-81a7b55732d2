import 'package:freezed_annotation/freezed_annotation.dart';

import '../enums/media_type_enum.dart';

part 'user_data_models.freezed.dart';
part 'user_data_models.g.dart';

// This robust helper function accepts any data type and safely handles it.
MediaType _mediaTypeFromJson(dynamic value) {
  if (value is String) {
    return MediaType.fromString(value);
  }
  // If the value from the database is null or not a String, default to 'unknown'.
  // This prevents the app from crashing.
  return MediaType.unknown;
}

// This helper ensures the enum is always saved correctly as a string.
String _mediaTypeToJson(MediaType type) {
  return type.toJson();
}

// Enum for sync status
enum SyncStatus { pending, synced, failed }

@freezed
abstract class FavoriteItem with _$FavoriteItem {
  const factory FavoriteItem({
    required String id,
    required String userId,
    required String itemId,
    @JsonKey(fromJson: _mediaTypeFromJson, toJson: _mediaTypeToJson)
    required MediaType type,
    required DateTime createdAt,
    @Default(SyncStatus.pending) SyncStatus syncStatus,
  }) = _FavoriteItem;

  factory FavoriteItem.fromJson(Map<String, dynamic> json) =>
      _$FavoriteItemFromJson(json);
}

@freezed
abstract class BookmarkItem with _$BookmarkItem {
  const factory BookmarkItem({
    required String id,
    required String userId,
    required String itemId,
    @JsonKey(fromJson: _mediaTypeFromJson, toJson: _mediaTypeToJson)
    required MediaType type,
    required DateTime createdAt,
    @Default(SyncStatus.pending) SyncStatus syncStatus,
  }) = _BookmarkItem;

  factory BookmarkItem.fromJson(Map<String, dynamic> json) =>
      _$BookmarkItemFromJson(json);
}
