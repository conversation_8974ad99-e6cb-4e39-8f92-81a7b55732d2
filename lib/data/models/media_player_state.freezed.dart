// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'media_player_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MediaPlayerState {
  ContentModel? get mediaItem;
  String? get errorMessage;
  bool get isDownloading;
  double? get downloadProgress;
  String? get downloadedFilePath;

  /// Create a copy of MediaPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MediaPlayerStateCopyWith<MediaPlayerState> get copyWith =>
      _$MediaPlayerStateCopyWithImpl<MediaPlayerState>(
          this as MediaPlayerState, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MediaPlayerState &&
            (identical(other.mediaItem, mediaItem) ||
                other.mediaItem == mediaItem) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isDownloading, isDownloading) ||
                other.isDownloading == isDownloading) &&
            (identical(other.downloadProgress, downloadProgress) ||
                other.downloadProgress == downloadProgress) &&
            (identical(other.downloadedFilePath, downloadedFilePath) ||
                other.downloadedFilePath == downloadedFilePath));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mediaItem, errorMessage,
      isDownloading, downloadProgress, downloadedFilePath);

  @override
  String toString() {
    return 'MediaPlayerState(mediaItem: $mediaItem, errorMessage: $errorMessage, isDownloading: $isDownloading, downloadProgress: $downloadProgress, downloadedFilePath: $downloadedFilePath)';
  }
}

/// @nodoc
abstract mixin class $MediaPlayerStateCopyWith<$Res> {
  factory $MediaPlayerStateCopyWith(
          MediaPlayerState value, $Res Function(MediaPlayerState) _then) =
      _$MediaPlayerStateCopyWithImpl;
  @useResult
  $Res call(
      {ContentModel? mediaItem,
      String? errorMessage,
      bool isDownloading,
      double? downloadProgress,
      String? downloadedFilePath});

  $ContentModelCopyWith<$Res>? get mediaItem;
}

/// @nodoc
class _$MediaPlayerStateCopyWithImpl<$Res>
    implements $MediaPlayerStateCopyWith<$Res> {
  _$MediaPlayerStateCopyWithImpl(this._self, this._then);

  final MediaPlayerState _self;
  final $Res Function(MediaPlayerState) _then;

  /// Create a copy of MediaPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? mediaItem = freezed,
    Object? errorMessage = freezed,
    Object? isDownloading = null,
    Object? downloadProgress = freezed,
    Object? downloadedFilePath = freezed,
  }) {
    return _then(_self.copyWith(
      mediaItem: freezed == mediaItem
          ? _self.mediaItem
          : mediaItem // ignore: cast_nullable_to_non_nullable
              as ContentModel?,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      isDownloading: null == isDownloading
          ? _self.isDownloading
          : isDownloading // ignore: cast_nullable_to_non_nullable
              as bool,
      downloadProgress: freezed == downloadProgress
          ? _self.downloadProgress
          : downloadProgress // ignore: cast_nullable_to_non_nullable
              as double?,
      downloadedFilePath: freezed == downloadedFilePath
          ? _self.downloadedFilePath
          : downloadedFilePath // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of MediaPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContentModelCopyWith<$Res>? get mediaItem {
    if (_self.mediaItem == null) {
      return null;
    }

    return $ContentModelCopyWith<$Res>(_self.mediaItem!, (value) {
      return _then(_self.copyWith(mediaItem: value));
    });
  }
}

/// @nodoc

class _MediaPlayerState implements MediaPlayerState {
  const _MediaPlayerState(
      {required this.mediaItem,
      this.errorMessage,
      this.isDownloading = false,
      this.downloadProgress,
      this.downloadedFilePath});

  @override
  final ContentModel? mediaItem;
  @override
  final String? errorMessage;
  @override
  @JsonKey()
  final bool isDownloading;
  @override
  final double? downloadProgress;
  @override
  final String? downloadedFilePath;

  /// Create a copy of MediaPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MediaPlayerStateCopyWith<_MediaPlayerState> get copyWith =>
      __$MediaPlayerStateCopyWithImpl<_MediaPlayerState>(this, _$identity);

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MediaPlayerState &&
            (identical(other.mediaItem, mediaItem) ||
                other.mediaItem == mediaItem) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.isDownloading, isDownloading) ||
                other.isDownloading == isDownloading) &&
            (identical(other.downloadProgress, downloadProgress) ||
                other.downloadProgress == downloadProgress) &&
            (identical(other.downloadedFilePath, downloadedFilePath) ||
                other.downloadedFilePath == downloadedFilePath));
  }

  @override
  int get hashCode => Object.hash(runtimeType, mediaItem, errorMessage,
      isDownloading, downloadProgress, downloadedFilePath);

  @override
  String toString() {
    return 'MediaPlayerState(mediaItem: $mediaItem, errorMessage: $errorMessage, isDownloading: $isDownloading, downloadProgress: $downloadProgress, downloadedFilePath: $downloadedFilePath)';
  }
}

/// @nodoc
abstract mixin class _$MediaPlayerStateCopyWith<$Res>
    implements $MediaPlayerStateCopyWith<$Res> {
  factory _$MediaPlayerStateCopyWith(
          _MediaPlayerState value, $Res Function(_MediaPlayerState) _then) =
      __$MediaPlayerStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {ContentModel? mediaItem,
      String? errorMessage,
      bool isDownloading,
      double? downloadProgress,
      String? downloadedFilePath});

  @override
  $ContentModelCopyWith<$Res>? get mediaItem;
}

/// @nodoc
class __$MediaPlayerStateCopyWithImpl<$Res>
    implements _$MediaPlayerStateCopyWith<$Res> {
  __$MediaPlayerStateCopyWithImpl(this._self, this._then);

  final _MediaPlayerState _self;
  final $Res Function(_MediaPlayerState) _then;

  /// Create a copy of MediaPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? mediaItem = freezed,
    Object? errorMessage = freezed,
    Object? isDownloading = null,
    Object? downloadProgress = freezed,
    Object? downloadedFilePath = freezed,
  }) {
    return _then(_MediaPlayerState(
      mediaItem: freezed == mediaItem
          ? _self.mediaItem
          : mediaItem // ignore: cast_nullable_to_non_nullable
              as ContentModel?,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      isDownloading: null == isDownloading
          ? _self.isDownloading
          : isDownloading // ignore: cast_nullable_to_non_nullable
              as bool,
      downloadProgress: freezed == downloadProgress
          ? _self.downloadProgress
          : downloadProgress // ignore: cast_nullable_to_non_nullable
              as double?,
      downloadedFilePath: freezed == downloadedFilePath
          ? _self.downloadedFilePath
          : downloadedFilePath // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of MediaPlayerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContentModelCopyWith<$Res>? get mediaItem {
    if (_self.mediaItem == null) {
      return null;
    }

    return $ContentModelCopyWith<$Res>(_self.mediaItem!, (value) {
      return _then(_self.copyWith(mediaItem: value));
    });
  }
}

// dart format on
