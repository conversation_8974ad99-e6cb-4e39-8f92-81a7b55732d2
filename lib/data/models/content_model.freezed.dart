// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'content_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ContentModel {
  int get id;
  String get title;
  String? get description;
  @JsonKey(name: 'content_type')
  String get contentType;
  @JsonKey(name: 'content_type_label')
  String get contentTypeLabel;
  int get visits;
  @JsonKey(name: 'category_id')
  int get categoryId;
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt; // Polymorphic data fields
  @JsonKey(name: 'audio_data')
  AudioData? get audioData;
  @JsonKey(name: 'video_data')
  VideoData? get videoData;
  @JsonKey(name: 'article_data')
  ArticleData? get articleData;
  @JsonKey(name: 'book_data')
  BookData? get bookData;
  @JsonKey(name: 'post_data')
  PostData? get postData;

  /// Create a copy of ContentModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContentModelCopyWith<ContentModel> get copyWith =>
      _$ContentModelCopyWithImpl<ContentModel>(
          this as ContentModel, _$identity);

  /// Serializes this ContentModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ContentModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.contentType, contentType) ||
                other.contentType == contentType) &&
            (identical(other.contentTypeLabel, contentTypeLabel) ||
                other.contentTypeLabel == contentTypeLabel) &&
            (identical(other.visits, visits) || other.visits == visits) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.audioData, audioData) ||
                other.audioData == audioData) &&
            (identical(other.videoData, videoData) ||
                other.videoData == videoData) &&
            (identical(other.articleData, articleData) ||
                other.articleData == articleData) &&
            (identical(other.bookData, bookData) ||
                other.bookData == bookData) &&
            (identical(other.postData, postData) ||
                other.postData == postData));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      description,
      contentType,
      contentTypeLabel,
      visits,
      categoryId,
      createdAt,
      updatedAt,
      audioData,
      videoData,
      articleData,
      bookData,
      postData);

  @override
  String toString() {
    return 'ContentModel(id: $id, title: $title, description: $description, contentType: $contentType, contentTypeLabel: $contentTypeLabel, visits: $visits, categoryId: $categoryId, createdAt: $createdAt, updatedAt: $updatedAt, audioData: $audioData, videoData: $videoData, articleData: $articleData, bookData: $bookData, postData: $postData)';
  }
}

/// @nodoc
abstract mixin class $ContentModelCopyWith<$Res> {
  factory $ContentModelCopyWith(
          ContentModel value, $Res Function(ContentModel) _then) =
      _$ContentModelCopyWithImpl;
  @useResult
  $Res call(
      {int id,
      String title,
      String? description,
      @JsonKey(name: 'content_type') String contentType,
      @JsonKey(name: 'content_type_label') String contentTypeLabel,
      int visits,
      @JsonKey(name: 'category_id') int categoryId,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt,
      @JsonKey(name: 'audio_data') AudioData? audioData,
      @JsonKey(name: 'video_data') VideoData? videoData,
      @JsonKey(name: 'article_data') ArticleData? articleData,
      @JsonKey(name: 'book_data') BookData? bookData,
      @JsonKey(name: 'post_data') PostData? postData});

  $AudioDataCopyWith<$Res>? get audioData;
  $VideoDataCopyWith<$Res>? get videoData;
  $ArticleDataCopyWith<$Res>? get articleData;
  $BookDataCopyWith<$Res>? get bookData;
  $PostDataCopyWith<$Res>? get postData;
}

/// @nodoc
class _$ContentModelCopyWithImpl<$Res> implements $ContentModelCopyWith<$Res> {
  _$ContentModelCopyWithImpl(this._self, this._then);

  final ContentModel _self;
  final $Res Function(ContentModel) _then;

  /// Create a copy of ContentModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? contentType = null,
    Object? contentTypeLabel = null,
    Object? visits = null,
    Object? categoryId = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? audioData = freezed,
    Object? videoData = freezed,
    Object? articleData = freezed,
    Object? bookData = freezed,
    Object? postData = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      contentType: null == contentType
          ? _self.contentType
          : contentType // ignore: cast_nullable_to_non_nullable
              as String,
      contentTypeLabel: null == contentTypeLabel
          ? _self.contentTypeLabel
          : contentTypeLabel // ignore: cast_nullable_to_non_nullable
              as String,
      visits: null == visits
          ? _self.visits
          : visits // ignore: cast_nullable_to_non_nullable
              as int,
      categoryId: null == categoryId
          ? _self.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      audioData: freezed == audioData
          ? _self.audioData
          : audioData // ignore: cast_nullable_to_non_nullable
              as AudioData?,
      videoData: freezed == videoData
          ? _self.videoData
          : videoData // ignore: cast_nullable_to_non_nullable
              as VideoData?,
      articleData: freezed == articleData
          ? _self.articleData
          : articleData // ignore: cast_nullable_to_non_nullable
              as ArticleData?,
      bookData: freezed == bookData
          ? _self.bookData
          : bookData // ignore: cast_nullable_to_non_nullable
              as BookData?,
      postData: freezed == postData
          ? _self.postData
          : postData // ignore: cast_nullable_to_non_nullable
              as PostData?,
    ));
  }

  /// Create a copy of ContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AudioDataCopyWith<$Res>? get audioData {
    if (_self.audioData == null) {
      return null;
    }

    return $AudioDataCopyWith<$Res>(_self.audioData!, (value) {
      return _then(_self.copyWith(audioData: value));
    });
  }

  /// Create a copy of ContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VideoDataCopyWith<$Res>? get videoData {
    if (_self.videoData == null) {
      return null;
    }

    return $VideoDataCopyWith<$Res>(_self.videoData!, (value) {
      return _then(_self.copyWith(videoData: value));
    });
  }

  /// Create a copy of ContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ArticleDataCopyWith<$Res>? get articleData {
    if (_self.articleData == null) {
      return null;
    }

    return $ArticleDataCopyWith<$Res>(_self.articleData!, (value) {
      return _then(_self.copyWith(articleData: value));
    });
  }

  /// Create a copy of ContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BookDataCopyWith<$Res>? get bookData {
    if (_self.bookData == null) {
      return null;
    }

    return $BookDataCopyWith<$Res>(_self.bookData!, (value) {
      return _then(_self.copyWith(bookData: value));
    });
  }

  /// Create a copy of ContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PostDataCopyWith<$Res>? get postData {
    if (_self.postData == null) {
      return null;
    }

    return $PostDataCopyWith<$Res>(_self.postData!, (value) {
      return _then(_self.copyWith(postData: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _ContentModel implements ContentModel {
  const _ContentModel(
      {required this.id,
      required this.title,
      this.description,
      @JsonKey(name: 'content_type') required this.contentType,
      @JsonKey(name: 'content_type_label') required this.contentTypeLabel,
      required this.visits,
      @JsonKey(name: 'category_id') required this.categoryId,
      @JsonKey(name: 'created_at') required this.createdAt,
      @JsonKey(name: 'updated_at') required this.updatedAt,
      @JsonKey(name: 'audio_data') this.audioData,
      @JsonKey(name: 'video_data') this.videoData,
      @JsonKey(name: 'article_data') this.articleData,
      @JsonKey(name: 'book_data') this.bookData,
      @JsonKey(name: 'post_data') this.postData});
  factory _ContentModel.fromJson(Map<String, dynamic> json) =>
      _$ContentModelFromJson(json);

  @override
  final int id;
  @override
  final String title;
  @override
  final String? description;
  @override
  @JsonKey(name: 'content_type')
  final String contentType;
  @override
  @JsonKey(name: 'content_type_label')
  final String contentTypeLabel;
  @override
  final int visits;
  @override
  @JsonKey(name: 'category_id')
  final int categoryId;
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
// Polymorphic data fields
  @override
  @JsonKey(name: 'audio_data')
  final AudioData? audioData;
  @override
  @JsonKey(name: 'video_data')
  final VideoData? videoData;
  @override
  @JsonKey(name: 'article_data')
  final ArticleData? articleData;
  @override
  @JsonKey(name: 'book_data')
  final BookData? bookData;
  @override
  @JsonKey(name: 'post_data')
  final PostData? postData;

  /// Create a copy of ContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContentModelCopyWith<_ContentModel> get copyWith =>
      __$ContentModelCopyWithImpl<_ContentModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContentModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContentModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.contentType, contentType) ||
                other.contentType == contentType) &&
            (identical(other.contentTypeLabel, contentTypeLabel) ||
                other.contentTypeLabel == contentTypeLabel) &&
            (identical(other.visits, visits) || other.visits == visits) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.audioData, audioData) ||
                other.audioData == audioData) &&
            (identical(other.videoData, videoData) ||
                other.videoData == videoData) &&
            (identical(other.articleData, articleData) ||
                other.articleData == articleData) &&
            (identical(other.bookData, bookData) ||
                other.bookData == bookData) &&
            (identical(other.postData, postData) ||
                other.postData == postData));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      title,
      description,
      contentType,
      contentTypeLabel,
      visits,
      categoryId,
      createdAt,
      updatedAt,
      audioData,
      videoData,
      articleData,
      bookData,
      postData);

  @override
  String toString() {
    return 'ContentModel(id: $id, title: $title, description: $description, contentType: $contentType, contentTypeLabel: $contentTypeLabel, visits: $visits, categoryId: $categoryId, createdAt: $createdAt, updatedAt: $updatedAt, audioData: $audioData, videoData: $videoData, articleData: $articleData, bookData: $bookData, postData: $postData)';
  }
}

/// @nodoc
abstract mixin class _$ContentModelCopyWith<$Res>
    implements $ContentModelCopyWith<$Res> {
  factory _$ContentModelCopyWith(
          _ContentModel value, $Res Function(_ContentModel) _then) =
      __$ContentModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int id,
      String title,
      String? description,
      @JsonKey(name: 'content_type') String contentType,
      @JsonKey(name: 'content_type_label') String contentTypeLabel,
      int visits,
      @JsonKey(name: 'category_id') int categoryId,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt,
      @JsonKey(name: 'audio_data') AudioData? audioData,
      @JsonKey(name: 'video_data') VideoData? videoData,
      @JsonKey(name: 'article_data') ArticleData? articleData,
      @JsonKey(name: 'book_data') BookData? bookData,
      @JsonKey(name: 'post_data') PostData? postData});

  @override
  $AudioDataCopyWith<$Res>? get audioData;
  @override
  $VideoDataCopyWith<$Res>? get videoData;
  @override
  $ArticleDataCopyWith<$Res>? get articleData;
  @override
  $BookDataCopyWith<$Res>? get bookData;
  @override
  $PostDataCopyWith<$Res>? get postData;
}

/// @nodoc
class __$ContentModelCopyWithImpl<$Res>
    implements _$ContentModelCopyWith<$Res> {
  __$ContentModelCopyWithImpl(this._self, this._then);

  final _ContentModel _self;
  final $Res Function(_ContentModel) _then;

  /// Create a copy of ContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? title = null,
    Object? description = freezed,
    Object? contentType = null,
    Object? contentTypeLabel = null,
    Object? visits = null,
    Object? categoryId = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? audioData = freezed,
    Object? videoData = freezed,
    Object? articleData = freezed,
    Object? bookData = freezed,
    Object? postData = freezed,
  }) {
    return _then(_ContentModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      description: freezed == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      contentType: null == contentType
          ? _self.contentType
          : contentType // ignore: cast_nullable_to_non_nullable
              as String,
      contentTypeLabel: null == contentTypeLabel
          ? _self.contentTypeLabel
          : contentTypeLabel // ignore: cast_nullable_to_non_nullable
              as String,
      visits: null == visits
          ? _self.visits
          : visits // ignore: cast_nullable_to_non_nullable
              as int,
      categoryId: null == categoryId
          ? _self.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      audioData: freezed == audioData
          ? _self.audioData
          : audioData // ignore: cast_nullable_to_non_nullable
              as AudioData?,
      videoData: freezed == videoData
          ? _self.videoData
          : videoData // ignore: cast_nullable_to_non_nullable
              as VideoData?,
      articleData: freezed == articleData
          ? _self.articleData
          : articleData // ignore: cast_nullable_to_non_nullable
              as ArticleData?,
      bookData: freezed == bookData
          ? _self.bookData
          : bookData // ignore: cast_nullable_to_non_nullable
              as BookData?,
      postData: freezed == postData
          ? _self.postData
          : postData // ignore: cast_nullable_to_non_nullable
              as PostData?,
    ));
  }

  /// Create a copy of ContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $AudioDataCopyWith<$Res>? get audioData {
    if (_self.audioData == null) {
      return null;
    }

    return $AudioDataCopyWith<$Res>(_self.audioData!, (value) {
      return _then(_self.copyWith(audioData: value));
    });
  }

  /// Create a copy of ContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VideoDataCopyWith<$Res>? get videoData {
    if (_self.videoData == null) {
      return null;
    }

    return $VideoDataCopyWith<$Res>(_self.videoData!, (value) {
      return _then(_self.copyWith(videoData: value));
    });
  }

  /// Create a copy of ContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ArticleDataCopyWith<$Res>? get articleData {
    if (_self.articleData == null) {
      return null;
    }

    return $ArticleDataCopyWith<$Res>(_self.articleData!, (value) {
      return _then(_self.copyWith(articleData: value));
    });
  }

  /// Create a copy of ContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BookDataCopyWith<$Res>? get bookData {
    if (_self.bookData == null) {
      return null;
    }

    return $BookDataCopyWith<$Res>(_self.bookData!, (value) {
      return _then(_self.copyWith(bookData: value));
    });
  }

  /// Create a copy of ContentModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PostDataCopyWith<$Res>? get postData {
    if (_self.postData == null) {
      return null;
    }

    return $PostDataCopyWith<$Res>(_self.postData!, (value) {
      return _then(_self.copyWith(postData: value));
    });
  }
}

/// @nodoc
mixin _$AudioData {
  int get id;
  @JsonKey(name: 'audio_file')
  String? get audioFile;
  @JsonKey(name: 'audio_url')
  String get audioUrl;
  @JsonKey(name: 'image_url')
  String? get imageUrl;
  String? get duration;
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt;
  String? get transcript;

  /// Create a copy of AudioData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AudioDataCopyWith<AudioData> get copyWith =>
      _$AudioDataCopyWithImpl<AudioData>(this as AudioData, _$identity);

  /// Serializes this AudioData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AudioData &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.audioFile, audioFile) ||
                other.audioFile == audioFile) &&
            (identical(other.audioUrl, audioUrl) ||
                other.audioUrl == audioUrl) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.transcript, transcript) ||
                other.transcript == transcript));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, audioFile, audioUrl,
      imageUrl, duration, createdAt, updatedAt, transcript);

  @override
  String toString() {
    return 'AudioData(id: $id, audioFile: $audioFile, audioUrl: $audioUrl, imageUrl: $imageUrl, duration: $duration, createdAt: $createdAt, updatedAt: $updatedAt, transcript: $transcript)';
  }
}

/// @nodoc
abstract mixin class $AudioDataCopyWith<$Res> {
  factory $AudioDataCopyWith(AudioData value, $Res Function(AudioData) _then) =
      _$AudioDataCopyWithImpl;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'audio_file') String? audioFile,
      @JsonKey(name: 'audio_url') String audioUrl,
      @JsonKey(name: 'image_url') String? imageUrl,
      String? duration,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt,
      String? transcript});
}

/// @nodoc
class _$AudioDataCopyWithImpl<$Res> implements $AudioDataCopyWith<$Res> {
  _$AudioDataCopyWithImpl(this._self, this._then);

  final AudioData _self;
  final $Res Function(AudioData) _then;

  /// Create a copy of AudioData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? audioFile = freezed,
    Object? audioUrl = null,
    Object? imageUrl = freezed,
    Object? duration = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? transcript = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      audioFile: freezed == audioFile
          ? _self.audioFile
          : audioFile // ignore: cast_nullable_to_non_nullable
              as String?,
      audioUrl: null == audioUrl
          ? _self.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _self.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      duration: freezed == duration
          ? _self.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      transcript: freezed == transcript
          ? _self.transcript
          : transcript // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _AudioData implements AudioData {
  const _AudioData(
      {required this.id,
      @JsonKey(name: 'audio_file') this.audioFile,
      @JsonKey(name: 'audio_url') required this.audioUrl,
      @JsonKey(name: 'image_url') this.imageUrl,
      this.duration,
      @JsonKey(name: 'created_at') required this.createdAt,
      @JsonKey(name: 'updated_at') required this.updatedAt,
      this.transcript});
  factory _AudioData.fromJson(Map<String, dynamic> json) =>
      _$AudioDataFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'audio_file')
  final String? audioFile;
  @override
  @JsonKey(name: 'audio_url')
  final String audioUrl;
  @override
  @JsonKey(name: 'image_url')
  final String? imageUrl;
  @override
  final String? duration;
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  @override
  final String? transcript;

  /// Create a copy of AudioData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AudioDataCopyWith<_AudioData> get copyWith =>
      __$AudioDataCopyWithImpl<_AudioData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AudioDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AudioData &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.audioFile, audioFile) ||
                other.audioFile == audioFile) &&
            (identical(other.audioUrl, audioUrl) ||
                other.audioUrl == audioUrl) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.transcript, transcript) ||
                other.transcript == transcript));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, audioFile, audioUrl,
      imageUrl, duration, createdAt, updatedAt, transcript);

  @override
  String toString() {
    return 'AudioData(id: $id, audioFile: $audioFile, audioUrl: $audioUrl, imageUrl: $imageUrl, duration: $duration, createdAt: $createdAt, updatedAt: $updatedAt, transcript: $transcript)';
  }
}

/// @nodoc
abstract mixin class _$AudioDataCopyWith<$Res>
    implements $AudioDataCopyWith<$Res> {
  factory _$AudioDataCopyWith(
          _AudioData value, $Res Function(_AudioData) _then) =
      __$AudioDataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'audio_file') String? audioFile,
      @JsonKey(name: 'audio_url') String audioUrl,
      @JsonKey(name: 'image_url') String? imageUrl,
      String? duration,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt,
      String? transcript});
}

/// @nodoc
class __$AudioDataCopyWithImpl<$Res> implements _$AudioDataCopyWith<$Res> {
  __$AudioDataCopyWithImpl(this._self, this._then);

  final _AudioData _self;
  final $Res Function(_AudioData) _then;

  /// Create a copy of AudioData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? audioFile = freezed,
    Object? audioUrl = null,
    Object? imageUrl = freezed,
    Object? duration = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? transcript = freezed,
  }) {
    return _then(_AudioData(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      audioFile: freezed == audioFile
          ? _self.audioFile
          : audioFile // ignore: cast_nullable_to_non_nullable
              as String?,
      audioUrl: null == audioUrl
          ? _self.audioUrl
          : audioUrl // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _self.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      duration: freezed == duration
          ? _self.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      transcript: freezed == transcript
          ? _self.transcript
          : transcript // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$VideoData {
  int get id;
  @JsonKey(name: 'youtube_video_id')
  String get youtubeVideoId;
  @JsonKey(name: 'thumbnail_url')
  String? get thumbnailUrl;
  String? get duration;
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt;

  /// Create a copy of VideoData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $VideoDataCopyWith<VideoData> get copyWith =>
      _$VideoDataCopyWithImpl<VideoData>(this as VideoData, _$identity);

  /// Serializes this VideoData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is VideoData &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.youtubeVideoId, youtubeVideoId) ||
                other.youtubeVideoId == youtubeVideoId) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, youtubeVideoId, thumbnailUrl,
      duration, createdAt, updatedAt);

  @override
  String toString() {
    return 'VideoData(id: $id, youtubeVideoId: $youtubeVideoId, thumbnailUrl: $thumbnailUrl, duration: $duration, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// @nodoc
abstract mixin class $VideoDataCopyWith<$Res> {
  factory $VideoDataCopyWith(VideoData value, $Res Function(VideoData) _then) =
      _$VideoDataCopyWithImpl;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'youtube_video_id') String youtubeVideoId,
      @JsonKey(name: 'thumbnail_url') String? thumbnailUrl,
      String? duration,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});
}

/// @nodoc
class _$VideoDataCopyWithImpl<$Res> implements $VideoDataCopyWith<$Res> {
  _$VideoDataCopyWithImpl(this._self, this._then);

  final VideoData _self;
  final $Res Function(VideoData) _then;

  /// Create a copy of VideoData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? youtubeVideoId = null,
    Object? thumbnailUrl = freezed,
    Object? duration = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      youtubeVideoId: null == youtubeVideoId
          ? _self.youtubeVideoId
          : youtubeVideoId // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnailUrl: freezed == thumbnailUrl
          ? _self.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      duration: freezed == duration
          ? _self.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _VideoData implements VideoData {
  const _VideoData(
      {required this.id,
      @JsonKey(name: 'youtube_video_id') required this.youtubeVideoId,
      @JsonKey(name: 'thumbnail_url') this.thumbnailUrl,
      this.duration,
      @JsonKey(name: 'created_at') required this.createdAt,
      @JsonKey(name: 'updated_at') required this.updatedAt});
  factory _VideoData.fromJson(Map<String, dynamic> json) =>
      _$VideoDataFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'youtube_video_id')
  final String youtubeVideoId;
  @override
  @JsonKey(name: 'thumbnail_url')
  final String? thumbnailUrl;
  @override
  final String? duration;
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  /// Create a copy of VideoData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$VideoDataCopyWith<_VideoData> get copyWith =>
      __$VideoDataCopyWithImpl<_VideoData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$VideoDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _VideoData &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.youtubeVideoId, youtubeVideoId) ||
                other.youtubeVideoId == youtubeVideoId) &&
            (identical(other.thumbnailUrl, thumbnailUrl) ||
                other.thumbnailUrl == thumbnailUrl) &&
            (identical(other.duration, duration) ||
                other.duration == duration) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, youtubeVideoId, thumbnailUrl,
      duration, createdAt, updatedAt);

  @override
  String toString() {
    return 'VideoData(id: $id, youtubeVideoId: $youtubeVideoId, thumbnailUrl: $thumbnailUrl, duration: $duration, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// @nodoc
abstract mixin class _$VideoDataCopyWith<$Res>
    implements $VideoDataCopyWith<$Res> {
  factory _$VideoDataCopyWith(
          _VideoData value, $Res Function(_VideoData) _then) =
      __$VideoDataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'youtube_video_id') String youtubeVideoId,
      @JsonKey(name: 'thumbnail_url') String? thumbnailUrl,
      String? duration,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});
}

/// @nodoc
class __$VideoDataCopyWithImpl<$Res> implements _$VideoDataCopyWith<$Res> {
  __$VideoDataCopyWithImpl(this._self, this._then);

  final _VideoData _self;
  final $Res Function(_VideoData) _then;

  /// Create a copy of VideoData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? youtubeVideoId = null,
    Object? thumbnailUrl = freezed,
    Object? duration = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_VideoData(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      youtubeVideoId: null == youtubeVideoId
          ? _self.youtubeVideoId
          : youtubeVideoId // ignore: cast_nullable_to_non_nullable
              as String,
      thumbnailUrl: freezed == thumbnailUrl
          ? _self.thumbnailUrl
          : thumbnailUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      duration: freezed == duration
          ? _self.duration
          : duration // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
mixin _$ArticleData {
  int get id;
  String get text;
  @JsonKey(name: 'image_url')
  String? get imageUrl;
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt;

  /// Create a copy of ArticleData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ArticleDataCopyWith<ArticleData> get copyWith =>
      _$ArticleDataCopyWithImpl<ArticleData>(this as ArticleData, _$identity);

  /// Serializes this ArticleData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ArticleData &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, text, imageUrl, createdAt, updatedAt);

  @override
  String toString() {
    return 'ArticleData(id: $id, text: $text, imageUrl: $imageUrl, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// @nodoc
abstract mixin class $ArticleDataCopyWith<$Res> {
  factory $ArticleDataCopyWith(
          ArticleData value, $Res Function(ArticleData) _then) =
      _$ArticleDataCopyWithImpl;
  @useResult
  $Res call(
      {int id,
      String text,
      @JsonKey(name: 'image_url') String? imageUrl,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});
}

/// @nodoc
class _$ArticleDataCopyWithImpl<$Res> implements $ArticleDataCopyWith<$Res> {
  _$ArticleDataCopyWithImpl(this._self, this._then);

  final ArticleData _self;
  final $Res Function(ArticleData) _then;

  /// Create a copy of ArticleData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? text = null,
    Object? imageUrl = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      text: null == text
          ? _self.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _self.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _ArticleData implements ArticleData {
  const _ArticleData(
      {required this.id,
      required this.text,
      @JsonKey(name: 'image_url') this.imageUrl,
      @JsonKey(name: 'created_at') required this.createdAt,
      @JsonKey(name: 'updated_at') required this.updatedAt});
  factory _ArticleData.fromJson(Map<String, dynamic> json) =>
      _$ArticleDataFromJson(json);

  @override
  final int id;
  @override
  final String text;
  @override
  @JsonKey(name: 'image_url')
  final String? imageUrl;
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  /// Create a copy of ArticleData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ArticleDataCopyWith<_ArticleData> get copyWith =>
      __$ArticleDataCopyWithImpl<_ArticleData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ArticleDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ArticleData &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, text, imageUrl, createdAt, updatedAt);

  @override
  String toString() {
    return 'ArticleData(id: $id, text: $text, imageUrl: $imageUrl, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// @nodoc
abstract mixin class _$ArticleDataCopyWith<$Res>
    implements $ArticleDataCopyWith<$Res> {
  factory _$ArticleDataCopyWith(
          _ArticleData value, $Res Function(_ArticleData) _then) =
      __$ArticleDataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int id,
      String text,
      @JsonKey(name: 'image_url') String? imageUrl,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});
}

/// @nodoc
class __$ArticleDataCopyWithImpl<$Res> implements _$ArticleDataCopyWith<$Res> {
  __$ArticleDataCopyWithImpl(this._self, this._then);

  final _ArticleData _self;
  final $Res Function(_ArticleData) _then;

  /// Create a copy of ArticleData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? text = null,
    Object? imageUrl = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_ArticleData(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      text: null == text
          ? _self.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _self.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
mixin _$PostData {
  int get id;
  String get text;
  @JsonKey(name: 'image_url')
  String? get imageUrl;
  @JsonKey(name: 'published_at')
  DateTime? get publishedAt;
  String? get url;
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt;

  /// Create a copy of PostData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $PostDataCopyWith<PostData> get copyWith =>
      _$PostDataCopyWithImpl<PostData>(this as PostData, _$identity);

  /// Serializes this PostData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is PostData &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.publishedAt, publishedAt) ||
                other.publishedAt == publishedAt) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, text, imageUrl, publishedAt, url, createdAt, updatedAt);

  @override
  String toString() {
    return 'PostData(id: $id, text: $text, imageUrl: $imageUrl, publishedAt: $publishedAt, url: $url, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// @nodoc
abstract mixin class $PostDataCopyWith<$Res> {
  factory $PostDataCopyWith(PostData value, $Res Function(PostData) _then) =
      _$PostDataCopyWithImpl;
  @useResult
  $Res call(
      {int id,
      String text,
      @JsonKey(name: 'image_url') String? imageUrl,
      @JsonKey(name: 'published_at') DateTime? publishedAt,
      String? url,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});
}

/// @nodoc
class _$PostDataCopyWithImpl<$Res> implements $PostDataCopyWith<$Res> {
  _$PostDataCopyWithImpl(this._self, this._then);

  final PostData _self;
  final $Res Function(PostData) _then;

  /// Create a copy of PostData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? text = null,
    Object? imageUrl = freezed,
    Object? publishedAt = freezed,
    Object? url = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      text: null == text
          ? _self.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _self.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      publishedAt: freezed == publishedAt
          ? _self.publishedAt
          : publishedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      url: freezed == url
          ? _self.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _PostData implements PostData {
  const _PostData(
      {required this.id,
      required this.text,
      @JsonKey(name: 'image_url') this.imageUrl,
      @JsonKey(name: 'published_at') this.publishedAt,
      this.url,
      @JsonKey(name: 'created_at') required this.createdAt,
      @JsonKey(name: 'updated_at') required this.updatedAt});
  factory _PostData.fromJson(Map<String, dynamic> json) =>
      _$PostDataFromJson(json);

  @override
  final int id;
  @override
  final String text;
  @override
  @JsonKey(name: 'image_url')
  final String? imageUrl;
  @override
  @JsonKey(name: 'published_at')
  final DateTime? publishedAt;
  @override
  final String? url;
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  /// Create a copy of PostData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$PostDataCopyWith<_PostData> get copyWith =>
      __$PostDataCopyWithImpl<_PostData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$PostDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _PostData &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.text, text) || other.text == text) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.publishedAt, publishedAt) ||
                other.publishedAt == publishedAt) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, text, imageUrl, publishedAt, url, createdAt, updatedAt);

  @override
  String toString() {
    return 'PostData(id: $id, text: $text, imageUrl: $imageUrl, publishedAt: $publishedAt, url: $url, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// @nodoc
abstract mixin class _$PostDataCopyWith<$Res>
    implements $PostDataCopyWith<$Res> {
  factory _$PostDataCopyWith(_PostData value, $Res Function(_PostData) _then) =
      __$PostDataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int id,
      String text,
      @JsonKey(name: 'image_url') String? imageUrl,
      @JsonKey(name: 'published_at') DateTime? publishedAt,
      String? url,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});
}

/// @nodoc
class __$PostDataCopyWithImpl<$Res> implements _$PostDataCopyWith<$Res> {
  __$PostDataCopyWithImpl(this._self, this._then);

  final _PostData _self;
  final $Res Function(_PostData) _then;

  /// Create a copy of PostData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? text = null,
    Object? imageUrl = freezed,
    Object? publishedAt = freezed,
    Object? url = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_PostData(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      text: null == text
          ? _self.text
          : text // ignore: cast_nullable_to_non_nullable
              as String,
      imageUrl: freezed == imageUrl
          ? _self.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      publishedAt: freezed == publishedAt
          ? _self.publishedAt
          : publishedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      url: freezed == url
          ? _self.url
          : url // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
mixin _$BookData {
  int get id;
  @JsonKey(name: 'file')
  String? get file;
  @JsonKey(name: 'file_url')
  String? get pdfUrl;
  @JsonKey(name: 'cover_image')
  String? get coverImage;
  @JsonKey(name: 'cover_image_url')
  String? get coverImageUrl;
  @JsonKey(name: 'pages_count')
  int? get pages;
  @JsonKey(name: 'published_date')
  String? get publishedDate;
  String? get publisher;
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt;

  /// Create a copy of BookData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $BookDataCopyWith<BookData> get copyWith =>
      _$BookDataCopyWithImpl<BookData>(this as BookData, _$identity);

  /// Serializes this BookData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is BookData &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.file, file) || other.file == file) &&
            (identical(other.pdfUrl, pdfUrl) || other.pdfUrl == pdfUrl) &&
            (identical(other.coverImage, coverImage) ||
                other.coverImage == coverImage) &&
            (identical(other.coverImageUrl, coverImageUrl) ||
                other.coverImageUrl == coverImageUrl) &&
            (identical(other.pages, pages) || other.pages == pages) &&
            (identical(other.publishedDate, publishedDate) ||
                other.publishedDate == publishedDate) &&
            (identical(other.publisher, publisher) ||
                other.publisher == publisher) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, file, pdfUrl, coverImage,
      coverImageUrl, pages, publishedDate, publisher, createdAt, updatedAt);

  @override
  String toString() {
    return 'BookData(id: $id, file: $file, pdfUrl: $pdfUrl, coverImage: $coverImage, coverImageUrl: $coverImageUrl, pages: $pages, publishedDate: $publishedDate, publisher: $publisher, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// @nodoc
abstract mixin class $BookDataCopyWith<$Res> {
  factory $BookDataCopyWith(BookData value, $Res Function(BookData) _then) =
      _$BookDataCopyWithImpl;
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'file') String? file,
      @JsonKey(name: 'file_url') String? pdfUrl,
      @JsonKey(name: 'cover_image') String? coverImage,
      @JsonKey(name: 'cover_image_url') String? coverImageUrl,
      @JsonKey(name: 'pages_count') int? pages,
      @JsonKey(name: 'published_date') String? publishedDate,
      String? publisher,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});
}

/// @nodoc
class _$BookDataCopyWithImpl<$Res> implements $BookDataCopyWith<$Res> {
  _$BookDataCopyWithImpl(this._self, this._then);

  final BookData _self;
  final $Res Function(BookData) _then;

  /// Create a copy of BookData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? file = freezed,
    Object? pdfUrl = freezed,
    Object? coverImage = freezed,
    Object? coverImageUrl = freezed,
    Object? pages = freezed,
    Object? publishedDate = freezed,
    Object? publisher = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      file: freezed == file
          ? _self.file
          : file // ignore: cast_nullable_to_non_nullable
              as String?,
      pdfUrl: freezed == pdfUrl
          ? _self.pdfUrl
          : pdfUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      coverImage: freezed == coverImage
          ? _self.coverImage
          : coverImage // ignore: cast_nullable_to_non_nullable
              as String?,
      coverImageUrl: freezed == coverImageUrl
          ? _self.coverImageUrl
          : coverImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      pages: freezed == pages
          ? _self.pages
          : pages // ignore: cast_nullable_to_non_nullable
              as int?,
      publishedDate: freezed == publishedDate
          ? _self.publishedDate
          : publishedDate // ignore: cast_nullable_to_non_nullable
              as String?,
      publisher: freezed == publisher
          ? _self.publisher
          : publisher // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _BookData implements BookData {
  const _BookData(
      {required this.id,
      @JsonKey(name: 'file') this.file,
      @JsonKey(name: 'file_url') this.pdfUrl,
      @JsonKey(name: 'cover_image') this.coverImage,
      @JsonKey(name: 'cover_image_url') this.coverImageUrl,
      @JsonKey(name: 'pages_count') this.pages,
      @JsonKey(name: 'published_date') this.publishedDate,
      this.publisher,
      @JsonKey(name: 'created_at') required this.createdAt,
      @JsonKey(name: 'updated_at') required this.updatedAt});
  factory _BookData.fromJson(Map<String, dynamic> json) =>
      _$BookDataFromJson(json);

  @override
  final int id;
  @override
  @JsonKey(name: 'file')
  final String? file;
  @override
  @JsonKey(name: 'file_url')
  final String? pdfUrl;
  @override
  @JsonKey(name: 'cover_image')
  final String? coverImage;
  @override
  @JsonKey(name: 'cover_image_url')
  final String? coverImageUrl;
  @override
  @JsonKey(name: 'pages_count')
  final int? pages;
  @override
  @JsonKey(name: 'published_date')
  final String? publishedDate;
  @override
  final String? publisher;
  @override
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  /// Create a copy of BookData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$BookDataCopyWith<_BookData> get copyWith =>
      __$BookDataCopyWithImpl<_BookData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$BookDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _BookData &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.file, file) || other.file == file) &&
            (identical(other.pdfUrl, pdfUrl) || other.pdfUrl == pdfUrl) &&
            (identical(other.coverImage, coverImage) ||
                other.coverImage == coverImage) &&
            (identical(other.coverImageUrl, coverImageUrl) ||
                other.coverImageUrl == coverImageUrl) &&
            (identical(other.pages, pages) || other.pages == pages) &&
            (identical(other.publishedDate, publishedDate) ||
                other.publishedDate == publishedDate) &&
            (identical(other.publisher, publisher) ||
                other.publisher == publisher) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, file, pdfUrl, coverImage,
      coverImageUrl, pages, publishedDate, publisher, createdAt, updatedAt);

  @override
  String toString() {
    return 'BookData(id: $id, file: $file, pdfUrl: $pdfUrl, coverImage: $coverImage, coverImageUrl: $coverImageUrl, pages: $pages, publishedDate: $publishedDate, publisher: $publisher, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

/// @nodoc
abstract mixin class _$BookDataCopyWith<$Res>
    implements $BookDataCopyWith<$Res> {
  factory _$BookDataCopyWith(_BookData value, $Res Function(_BookData) _then) =
      __$BookDataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int id,
      @JsonKey(name: 'file') String? file,
      @JsonKey(name: 'file_url') String? pdfUrl,
      @JsonKey(name: 'cover_image') String? coverImage,
      @JsonKey(name: 'cover_image_url') String? coverImageUrl,
      @JsonKey(name: 'pages_count') int? pages,
      @JsonKey(name: 'published_date') String? publishedDate,
      String? publisher,
      @JsonKey(name: 'created_at') DateTime createdAt,
      @JsonKey(name: 'updated_at') DateTime updatedAt});
}

/// @nodoc
class __$BookDataCopyWithImpl<$Res> implements _$BookDataCopyWith<$Res> {
  __$BookDataCopyWithImpl(this._self, this._then);

  final _BookData _self;
  final $Res Function(_BookData) _then;

  /// Create a copy of BookData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? file = freezed,
    Object? pdfUrl = freezed,
    Object? coverImage = freezed,
    Object? coverImageUrl = freezed,
    Object? pages = freezed,
    Object? publishedDate = freezed,
    Object? publisher = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_BookData(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      file: freezed == file
          ? _self.file
          : file // ignore: cast_nullable_to_non_nullable
              as String?,
      pdfUrl: freezed == pdfUrl
          ? _self.pdfUrl
          : pdfUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      coverImage: freezed == coverImage
          ? _self.coverImage
          : coverImage // ignore: cast_nullable_to_non_nullable
              as String?,
      coverImageUrl: freezed == coverImageUrl
          ? _self.coverImageUrl
          : coverImageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      pages: freezed == pages
          ? _self.pages
          : pages // ignore: cast_nullable_to_non_nullable
              as int?,
      publishedDate: freezed == publishedDate
          ? _self.publishedDate
          : publishedDate // ignore: cast_nullable_to_non_nullable
              as String?,
      publisher: freezed == publisher
          ? _self.publisher
          : publisher // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

// dart format on
