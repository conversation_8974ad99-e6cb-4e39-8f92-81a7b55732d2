import 'package:freezed_annotation/freezed_annotation.dart';

import 'category_model.dart';
import 'content_model.dart';

part 'reels_response.freezed.dart';
part 'reels_response.g.dart';

// This model represents the nested "data" object in the response.
@freezed
abstract class ReelsData with _$ReelsData {
  const factory ReelsData({
    required CategoryModel category,
    required List<ContentModel> contents,
  }) = _ReelsData;

  factory ReelsData.fromJson(Map<String, dynamic> json) =>
      _$ReelsDataFromJson(json);
}

// This is the top-level response model.
@freezed
abstract class ReelsContentResponse with _$ReelsContentResponse {
  const factory ReelsContentResponse({
    required ReelsData data,
  }) = _ReelsContentResponse;

  factory ReelsContentResponse.fromJson(Map<String, dynamic> json) =>
      _$ReelsContentResponseFromJson(json);
}
