import 'package:freezed_annotation/freezed_annotation.dart';

part 'content_model.freezed.dart';
part 'content_model.g.dart';

// Model for the main content item - No changes were needed here.
@freezed
abstract class ContentModel with _$ContentModel {
  const factory ContentModel({
    required int id,
    required String title,
    String? description,
    @Json<PERSON>ey(name: 'content_type') required String contentType,
    @JsonKey(name: 'content_type_label') required String contentTypeLabel,
    required int visits,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'category_id') required int categoryId,
    @<PERSON>son<PERSON><PERSON>(name: 'created_at') required DateTime createdAt,
    @Json<PERSON>ey(name: 'updated_at') required DateTime updatedAt,
    // Polymorphic data fields
    @Json<PERSON>ey(name: 'audio_data') AudioData? audioData,
    @Json<PERSON>ey(name: 'video_data') VideoData? videoData,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'article_data') ArticleData? articleData,
    @JsonKey(name: 'book_data') BookData? bookData,
    @Json<PERSON>ey(name: 'post_data') PostData? postData,
  }) = _ContentModel;

  factory ContentModel.fromJson(Map<String, dynamic> json) =>
      _$ContentModelFromJson(json);
}

// --- Polymorphic Data Models (Updated with all API fields) ---

@freezed
abstract class AudioData with _$AudioData {
  const factory AudioData({
    required int id,
    @JsonKey(name: 'audio_file') String? audioFile,
    @JsonKey(name: 'audio_url') required String audioUrl,
    @JsonKey(name: 'image_url') String? imageUrl,
    String? duration,
    @JsonKey(name: 'created_at') required DateTime createdAt,
    @JsonKey(name: 'updated_at') required DateTime updatedAt,
    String? transcript,
  }) = _AudioData;

  factory AudioData.fromJson(Map<String, dynamic> json) =>
      _$AudioDataFromJson(json);
}

@freezed
abstract class VideoData with _$VideoData {
  const factory VideoData({
    required int id,
    @JsonKey(name: 'youtube_video_id') required String youtubeVideoId,
    @JsonKey(name: 'thumbnail_url') String? thumbnailUrl,
    String? duration,
    @JsonKey(name: 'created_at') required DateTime createdAt,
    @JsonKey(name: 'updated_at') required DateTime updatedAt,
  }) = _VideoData;

  factory VideoData.fromJson(Map<String, dynamic> json) =>
      _$VideoDataFromJson(json);
}

@freezed
abstract class ArticleData with _$ArticleData {
  const factory ArticleData({
    required int id,
    required String text,
    @JsonKey(name: 'image_url') String? imageUrl,
    @JsonKey(name: 'created_at') required DateTime createdAt,
    @JsonKey(name: 'updated_at') required DateTime updatedAt,
  }) = _ArticleData;

  factory ArticleData.fromJson(Map<String, dynamic> json) =>
      _$ArticleDataFromJson(json);
}

@freezed
abstract class PostData with _$PostData {
  const factory PostData({
    required int id,
    required String text,
    @JsonKey(name: 'image_url') String? imageUrl,
    @JsonKey(name: 'published_at') DateTime? publishedAt,
    String? url,
    @JsonKey(name: 'created_at') required DateTime createdAt,
    @JsonKey(name: 'updated_at') required DateTime updatedAt,
  }) = _PostData;

  factory PostData.fromJson(Map<String, dynamic> json) =>
      _$PostDataFromJson(json);
}

@freezed
abstract class BookData with _$BookData {
  const factory BookData({
    required int id,
    @JsonKey(name: 'file') String? file,
    @JsonKey(name: 'file_url') String? pdfUrl,
    @JsonKey(name: 'cover_image') String? coverImage,
    @JsonKey(name: 'cover_image_url') String? coverImageUrl,
    @JsonKey(name: 'pages_count') int? pages,
    @JsonKey(name: 'published_date') String? publishedDate,
    String? publisher,
    @JsonKey(name: 'created_at') required DateTime createdAt,
    @JsonKey(name: 'updated_at') required DateTime updatedAt,
  }) = _BookData;

  factory BookData.fromJson(Map<String, dynamic> json) =>
      _$BookDataFromJson(json);
}
