import 'package:injectable/injectable.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';

import '../enums/media_type_enum.dart';
import '../models/user_data_models.dart';

@injectable
class SQLiteHelper {
  static Database? _database;

  Future<Database> get database async {
    if (_database != null) {
      return _database!;
    }
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final String path = join(await getDatabasesPath(), 'app_database.db');
    return openDatabase(
      path,
      version: 3,
      onCreate: _createDatabase,
      onUpgrade: _onUpgradeDatabase,
    );
  }

  Future<void> _createDatabase(Database db, int version) async {
    final Batch batch = db.batch();
    batch.execute('''
      CREATE TABLE prefs(
        key TEXT PRIMARY KEY,
        value TEXT
      )
    ''');
    batch.execute('''
      CREATE TABLE favorites(
        id TEXT PRIMARY KEY,
        user_id TEXT,
        item_id TEXT,
        type TEXT NOT NULL,
        created_at TEXT,
        sync_status TEXT
      )
    ''');
    batch.execute('''
      CREATE TABLE bookmarks(
        id TEXT PRIMARY KEY,
        user_id TEXT,
        item_id TEXT,
        type TEXT NOT NULL,
        created_at TEXT,
        sync_status TEXT
      )
    ''');

    await batch.commit(noResult: true);
  }

  Future<void> _onUpgradeDatabase(
      Database db, int oldVersion, int newVersion) async {
    final Batch batch = db.batch();

    // If the user's old database version is less than 2, it means they
    // don't have the 'bookmarks' or 'favorites' tables yet. So, we create them.
    if (oldVersion < 2) {
      batch.execute('''
        CREATE TABLE favorites(
          id TEXT PRIMARY KEY,
          user_id TEXT,
          item_id TEXT,
          type TEXT NOT NULL,
          created_at TEXT,
          sync_status TEXT
        )
      ''');
      batch.execute('''
        CREATE TABLE bookmarks(
          id TEXT PRIMARY KEY,
          user_id TEXT,
          item_id TEXT,
          type TEXT NOT NULL,
          created_at TEXT,
          sync_status TEXT
        )
      ''');
    }

    // You can add more migrations here for future versions.
    // For example, if you add a 'notes' table in version 4, you would add:
    // if (oldVersion < 4) {
    //   batch.execute('CREATE TABLE notes(...)');
    // }

    await batch.commit();
  }

  /// REFACTOR: Create a single, reusable helper to prepare data for our models.
  /// This function converts snake_case keys from the DB to camelCase keys for the models
  /// and ensures required fields are not null.
  Map<String, dynamic> _prepareMapForModel(Map<String, dynamic> dbMap) {
    final Map<String, dynamic> modelMap = Map<String, dynamic>.from(dbMap);

    // This function checks for a key, renames it, and removes the old one.
    void renameKey(String from, String to) {
      if (modelMap.containsKey(from)) {
        modelMap[to] = modelMap.remove(from);
      }
    }

    renameKey('user_id', 'userId');
    renameKey('item_id', 'itemId');
    renameKey('created_at', 'createdAt');
    renameKey('last_updated', 'lastUpdated');
    renameKey('action_type', 'actionType');
    renameKey('position_seconds', 'positionSeconds');

    // Ensure the 'type' field is safe for decoding
    if (modelMap.containsKey('type') && modelMap['type'] == null) {
      modelMap['type'] = MediaType.unknown.toJson();
    }

    return modelMap;
  }

  // --- Preferences methods (Unchanged) ---
  Future<void> savePreference(String key, String value) async {
    final Database db = await database;
    await db.insert(
      'prefs',
      <String, Object?>{'key': key, 'value': value},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<String?> getPreference(String key, {String? defaultValue}) async {
    final Database db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'prefs',
      columns: <String>['value'],
      where: 'key = ?',
      whereArgs: <Object?>[key],
    );
    if (maps.isNotEmpty) {
      return maps.first['value'] as String;
    }
    return defaultValue;
  }

  // --- Favorites methods (Unchanged write methods) ---
  Future<String> addFavorite({
    required String userId,
    required String itemId,
    required String type,
  }) async {
    final Database db = await database;
    final String id = const Uuid().v4();
    final DateTime now = DateTime.now();
    await db.insert(
      'favorites',
      <String, Object?>{
        'id': id,
        'user_id': userId,
        'item_id': itemId,
        'type': type,
        'created_at': now.toIso8601String(),
        'sync_status': SyncStatus.pending.toString().split('.').last,
      },
    );
    return id;
  }

  Future<bool> removeFavorite({
    required String userId,
    required String itemId,
  }) async {
    final Database db = await database;
    final int count = await db.delete(
      'favorites',
      where: 'user_id = ? AND item_id = ?',
      whereArgs: <Object?>[userId, itemId],
    );
    return count > 0;
  }

  // REFACTOR: Use the helper for cleaner code.
  Future<List<FavoriteItem>> getUserFavorites(String userId) async {
    final Database db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'favorites',
      where: 'user_id = ?',
      whereArgs: <Object?>[userId],
      orderBy: 'created_at DESC',
    );
    return maps
        .map((Map<String, dynamic> map) =>
            FavoriteItem.fromJson(_prepareMapForModel(map)))
        .toList();
  }

  Future<bool> isFavorite({
    required String userId,
    required String itemId,
  }) async {
    final Database db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'favorites',
      where: 'user_id = ? AND item_id = ?',
      whereArgs: <Object?>[userId, itemId],
    );
    return maps.isNotEmpty;
  }

  // --- Bookmark methods (Unchanged write methods) ---
  Future<String> addBookmark({
    required String userId,
    required String itemId,
    required String type,
  }) async {
    final Database db = await database;
    final String id = const Uuid().v4();
    final DateTime now = DateTime.now();
    await db.insert(
      'bookmarks',
      <String, Object?>{
        'id': id,
        'user_id': userId,
        'item_id': itemId,
        'type': type,
        'created_at': now.toIso8601String(),
        'sync_status': SyncStatus.pending.toString().split('.').last,
      },
    );
    return id;
  }

  Future<bool> removeBookmark({
    required String userId,
    required String itemId,
  }) async {
    final Database db = await database;
    final int count = await db.delete(
      'bookmarks',
      where: 'user_id = ? AND item_id = ?',
      whereArgs: <Object?>[userId, itemId],
    );
    return count > 0;
  }

  // REFACTOR: Use the helper for cleaner code.
  Future<List<BookmarkItem>> getUserBookmarks(String userId) async {
    final Database db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bookmarks',
      where: 'user_id = ?',
      whereArgs: <Object?>[userId],
      orderBy: 'created_at DESC',
    );
    return maps
        .map((Map<String, dynamic> map) =>
            BookmarkItem.fromJson(_prepareMapForModel(map)))
        .toList();
  }

  Future<bool> isBookmarked({
    required String userId,
    required String itemId,
  }) async {
    final Database db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'bookmarks',
      where: 'user_id = ? AND item_id = ?',
      whereArgs: <Object?>[userId, itemId],
    );
    return maps.isNotEmpty;
  }

  // --- General methods (Unchanged) ---
  Future<void> clearDatabase() async {
    final Database db = await database;
    await db.delete('prefs');
    await db.delete('favorites');
    await db.delete('bookmarks');
  }

  Future<void> initSQLite() async {
    await database;
  }
}
