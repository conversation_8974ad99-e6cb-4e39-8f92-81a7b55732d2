import 'package:injectable/injectable.dart';
import 'package:sqflite/sqflite.dart';

import '../enums/media_type_enum.dart';
import '../models/user_data_models.dart';
import '../sqlite/sqlite_helper.dart';

@lazySingleton
class UserDataService {
  UserDataService(this._sqliteHelper);

  final SQLiteHelper _sqliteHelper;
  static const String _anonymousUserId = 'anonymous_user';

  // Favorites methods
  Future<bool> toggleFavorite({
    required String userId,
    required String itemId,
    required MediaType type,
  }) async {
    // Check if already favorited
    final bool isFavorited = await _sqliteHelper.isFavorite(
      userId: userId,
      itemId: itemId,
    );

    if (isFavorited) {
      // Remove from favorites
      await _sqliteHelper.removeFavorite(
        userId: userId,
        itemId: itemId,
      );
      return false;
    } else {
      // Add to favorites
      await _sqliteHelper.addFavorite(
        userId: userId,
        itemId: itemId,
        type: type.toJson(),
      );
      return true;
    }
  }

  Future<bool> isFavorite({
    required String userId,
    required String itemId,
  }) async {
    return _sqliteHelper.isFavorite(
      userId: userId,
      itemId: itemId,
    );
  }

  Future<List<FavoriteItem>> getFavorites({
    required String userId,
  }) async {
    return _sqliteHelper.getUserFavorites(userId);
  }

  // Bookmark methods
  Future<bool> toggleBookmark({
    required String userId,
    required String itemId,
    required MediaType type,
  }) async {
    // Check if already bookmarked
    final bool isBookmarked = await _sqliteHelper.isBookmarked(
      userId: userId,
      itemId: itemId,
    );

    if (isBookmarked) {
      // Remove from bookmarks
      await _sqliteHelper.removeBookmark(
        userId: userId,
        itemId: itemId,
      );
      return false;
    } else {
      // Add to bookmarks
      await _sqliteHelper.addBookmark(
        userId: userId,
        itemId: itemId,
        type: type.toJson(),
      );
      return true;
    }
  }

  Future<bool> isBookmarked({
    required String userId,
    required String itemId,
  }) async {
    return _sqliteHelper.isBookmarked(
      userId: userId,
      itemId: itemId,
    );
  }

  Future<List<BookmarkItem>> getBookmarks({
    required String userId,
  }) async {
    return _sqliteHelper.getUserBookmarks(userId);
  }

  // Clear user data
  Future<void> clearUserData({
    required String userId,
    bool clearFavorites = true,
    bool clearBookmarks = true,
  }) async {
    final Database db = await _sqliteHelper.database;

    // Use a transaction to ensure all deletes succeed or all fail
    await db.transaction((Transaction txn) async {
      if (clearFavorites) {
        await txn.delete(
          'favorites',
          where: 'user_id = ?',
          whereArgs: <Object?>[userId],
        );
      }

      if (clearBookmarks) {
        await txn.delete(
          'bookmarks',
          where: 'user_id = ?',
          whereArgs: <Object?>[userId],
        );
      }
    });
  }

  // Transfer anonymous data to user account
  Future<void> transferAnonymousData({
    required String userId,
  }) async {
    final Database db = await _sqliteHelper.database;

    // Use a transaction to ensure all updates succeed or all fail
    await db.transaction((Transaction txn) async {
      // Update favorites
      await txn.update(
        'favorites',
        <String, Object?>{'user_id': userId},
        where: 'user_id = ?',
        whereArgs: <Object?>[_anonymousUserId],
      );

      // Update bookmarks
      await txn.update(
        'bookmarks',
        <String, Object?>{'user_id': userId},
        where: 'user_id = ?',
        whereArgs: <Object?>[_anonymousUserId],
      );
    });
  }
}
