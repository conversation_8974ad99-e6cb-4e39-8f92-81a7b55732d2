import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';

import '../../constants/endpoints.dart';
import '../models/api_response.dart';
import '../models/category_model.dart';
import '../models/content_model.dart';

part 'api_service.g.dart';

@RestApi(baseUrl: Endpoints.baseUrl)
abstract class ApiService {
  factory ApiService(Dio dio, {String baseUrl}) = _ApiService;

  @GET(Endpoints.categories)
  // Your API returns a direct list of categories, so this is correct.
  Future<List<CategoryModel>> getCategories();

  @GET('${Endpoints.categories}/{categoryId}/contents')
  // Returns the PaginatedContentResponse model we defined.
  Future<PaginatedContentResponse> getContentsForCategory(
    @Path('categoryId') int categoryId, {
    @Query('page') int page = 1,
  });

  @GET(Endpoints.search)
  // Returns the SearchResultsResponse model we defined.
  Future<SearchResultsResponse> searchContent(
    @Query('query') String query, {
    @Query('categories') String? categories,
  });

  @GET('/content/{contentId}')
  // The visit endpoint returns a full ContentModel, so we assume this does too.
  Future<ContentModel> getContent(@Path('contentId') int contentId);

  @POST('/content/{contentId}/visit')
  // This endpoint also returns the updated content model.
  Future<ContentModel> visitContent(@Path('contentId') int contentId);
}
