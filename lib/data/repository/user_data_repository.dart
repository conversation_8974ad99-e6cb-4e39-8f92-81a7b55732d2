import 'package:injectable/injectable.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../di/components/service_locator.dart';
import '../../domain/repositories/user_data_repository_interface.dart';
import '../enums/media_type_enum.dart';
import '../models/user_data_models.dart';
import '../services/user_data_service.dart';

part 'user_data_repository.g.dart';

// This is now a generated provider

@Riverpod(keepAlive: true)
UserDataRepositoryInterface userDataRepository(Ref ref) {
  return getIt<UserDataRepositoryInterface>();
}

@Injectable(as: UserDataRepositoryInterface)
class UserDataRepository implements UserDataRepositoryInterface {
  UserDataRepository(this._userDataService);

  final UserDataService _userDataService;

  @override
  Future<bool> toggleFavorite({
    required String userId,
    required String itemId,
    required MediaType itemType,
  }) async {
    return _userDataService.toggleFavorite(
      userId: userId,
      itemId: itemId,
      type: itemType,
    );
  }

  @override
  Future<bool> isFavorite({
    required String userId,
    required String itemId,
  }) async {
    return _userDataService.isFavorite(
      userId: userId,
      itemId: itemId,
    );
  }

  @override
  Future<List<FavoriteItem>> getFavorites({
    required String userId,
  }) async {
    return _userDataService.getFavorites(userId: userId);
  }

  @override
  Future<bool> toggleBookmark({
    required String userId,
    required String itemId,
    required MediaType itemType,
  }) async {
    return _userDataService.toggleBookmark(
      userId: userId,
      itemId: itemId,
      type: itemType,
    );
  }

  @override
  Future<bool> isBookmarked({
    required String userId,
    required String itemId,
  }) async {
    return _userDataService.isBookmarked(
      userId: userId,
      itemId: itemId,
    );
  }

  @override
  Future<List<BookmarkItem>> getBookmarks({
    required String userId,
  }) async {
    return _userDataService.getBookmarks(userId: userId);
  }

  @override
  Future<void> clearUserData({
    required String userId,
    bool clearFavorites = true,
    bool clearBookmarks = true,
  }) async {
    return _userDataService.clearUserData(
      userId: userId,
      clearFavorites: clearFavorites,
      clearBookmarks: clearBookmarks,
    );
  }

  @override
  Future<void> transferAnonymousData({
    required String userId,
  }) async {
    return _userDataService.transferAnonymousData(userId: userId);
  }
}
