import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../core/error/app_error.dart';
import '../../core/logging/app_logger.dart';
import '../../di/components/service_locator.dart';
import '../models/api_response.dart';
import '../models/category_model.dart';
import '../models/content_model.dart';
import '../models/reels_response.dart';
import '../services/api_service.dart';
import 'media_repository_interface.dart';

part 'media_repository.g.dart';

@Riverpod(keepAlive: true) // Changed to keepAlive: true
MediaRepositoryInterface mediaRepository(Ref ref) {
  final Dio dio = getIt<Dio>();
  final ApiService apiService = ApiService(dio);
  return MediaRepository(apiService);
}

@Injectable(as: MediaRepositoryInterface)
class MediaRepository implements MediaRepositoryInterface {
  MediaRepository(this._apiService);
  final ApiService _apiService;

  @override
  Future<List<ContentModel>> getReels() async {
    const int reelsCategoryId = 33;
    final Dio dio = getIt<Dio>(); // Get Dio instance for direct call

    try {
      final Response<Map<String, dynamic>> response = // Explicit type argument
          await dio.get('/categories/$reelsCategoryId/contents');

      // Parse the response using our specific, type-safe model
      final ReelsContentResponse reelsResponse =
          ReelsContentResponse.fromJson(response.data!);

      return reelsResponse.data.contents;
    } on DioException catch (e) {
      // Re-throw as a custom AppError for consistent error handling
      throw fromDioException(e);
    } catch (e) {
      debugPrint('Failed to fetch or parse reels in repository: $e');
      throw AppError.unknown(message: 'Could not fetch reels: $e');
    }
  }

  @override
  Future<List<CategoryModel>> getCategories() async {
    try {
      return await _apiService.getCategories();
    } on DioException catch (e) {
      throw fromDioException(e); // Good: Converts Dio error to AppError
    } catch (e, s) {
      // Better: Convert any other exception to a specific AppError
      AppLogger.error('Failed to get categories', error: e, stackTrace: s);
      throw AppError.unknown(
          message: 'Could not fetch categories: $e', originalError: e);
    }
  }

  @override
  Future<PaginatedContentResponse> getContentsForCategory(int categoryId,
      {int page = 1}) {
    return _apiService.getContentsForCategory(categoryId, page: page);
  }

  @override
  Future<SearchResultsResponse> search(
      {required String query, List<int>? categoryIds}) {
    // Convert the list of integers to a comma-separated string if it exists and is not empty.
    final String? categoriesParam =
        (categoryIds != null && categoryIds.isNotEmpty)
            ? categoryIds.join(',')
            : null;

    // Call the single, flexible ApiService method.
    return _apiService.searchContent(query, categories: categoriesParam);
  }

  @override
  Future<ContentModel> getContentById(int contentId) async {
    try {
      // This both logs the visit and fetches the content details.
      final ContentModel content = await _apiService.visitContent(contentId);
      return content;
    } on DioException catch (e) {
      throw fromDioException(e);
    }
  }

  @override
  Future<ContentModel> visitContent(int contentId) async {
    try {
      final ContentModel content = await _apiService.visitContent(contentId);
      return content;
    } on DioException catch (e) {
      throw fromDioException(e);
    }
  }

  @override
  Future<CategoryModel> getContentByCategory(int categoryId) async {
    try {
      // Get the category first to get its name and other properties
      final List<CategoryModel> allCategories = await getCategories();
      final CategoryModel category = allCategories.firstWhere(
        (CategoryModel cat) => cat.id == categoryId,
        orElse: () => CategoryModel(id: categoryId, name: 'Unknown'),
      );

      // Get the contents for this category
      final PaginatedContentResponse response =
          await getContentsForCategory(categoryId);

      // Create a new CategoryModel with all the properties from the original
      // plus the contents from the response
      return CategoryModel(
        id: category.id,
        name: category.name,
        parentId: category.parentId,
        subcategoriesCount: category.subcategoriesCount,
        subcategories: category.subcategories,
        contents: response.data.contents,
      );
    } on DioException catch (e) {
      throw fromDioException(e);
    }
  }
}
