// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(mediaRepository)
const mediaRepositoryProvider = MediaRepositoryProvider._();

final class MediaRepositoryProvider extends $FunctionalProvider<
    MediaRepositoryInterface,
    MediaRepositoryInterface,
    MediaRepositoryInterface> with $Provider<MediaRepositoryInterface> {
  const MediaRepositoryProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'mediaRepositoryProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaRepositoryHash();

  @$internal
  @override
  $ProviderElement<MediaRepositoryInterface> $createElement(
          $ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  MediaRepositoryInterface create(Ref ref) {
    return mediaRepository(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(MediaRepositoryInterface value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<MediaRepositoryInterface>(value),
    );
  }
}

String _$mediaRepositoryHash() => r'9972b6e22d724f6a06a2e742a34e23d57931a8bb';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
