import '../models/api_response.dart';
import '../models/category_model.dart';
import '../models/content_model.dart';

abstract class MediaRepositoryInterface {
  Future<List<CategoryModel>> getCategories();

  Future<PaginatedContentResponse> getContentsForCategory(int categoryId,
      {int page = 1});

  // NEW: Add a dedicated method for getting reels.
  Future<List<ContentModel>> getReels();

  Future<SearchResultsResponse> search(
      {required String query, List<int>? categoryIds});

  Future<ContentModel> getContentById(int contentId);

  Future<ContentModel> visitContent(int contentId);

  Future<CategoryModel> getContentByCategory(int categoryId);
}
