import 'package:get_storage/get_storage.dart';
import 'package:injectable/injectable.dart';

// REMOVED: import '../services/secure_storage_service.dart';

const String emailKey = 'email';
const String themeKey = 'theme_mode';
const String languageKey = 'language';
const String lastSyncKey = 'last_sync';

@injectable
class GetStoreHelper {
  // MODIFIED: Constructor no longer takes SecureStorageService.
  GetStoreHelper(this.getStorage);

  final GetStorage getStorage;

  // === NON-SENSITIVE DATA METHODS (using GetStorage) ===

  /// Save email (non-sensitive for auto-fill)
  Future<void> saveEmail(String email) async {
    await getStorage.write(emailKey, email);
  }

  /// Get saved email
  String? getEmail() {
    return getStorage.read(emailKey);
  }

  /// Save theme mode preference
  Future<void> saveThemeMode(String themeMode) async {
    await getStorage.write(themeKey, themeMode);
  }

  /// Get theme mode preference
  String? getThemeMode() {
    return getStorage.read(themeKey);
  }

  /// Save language preference
  Future<void> saveLanguage(String language) async {
    await getStorage.write(languageKey, language);
  }

  /// Get language preference
  String? getLanguage() {
    return getStorage.read(languageKey);
  }

  /// Save last sync timestamp
  Future<void> saveLastSync(DateTime timestamp) async {
    await getStorage.write(lastSyncKey, timestamp.toIso8601String());
  }

  /// Get last sync timestamp
  DateTime? getLastSync() {
    final String? timestamp = getStorage.read(lastSyncKey);
    return timestamp != null ? DateTime.tryParse(timestamp) : null;
  }

  /// Clear all non-sensitive data
  void clearAll() {
    getStorage.erase();
  }
}
