import 'package:audio_video_progress_bar/audio_video_progress_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart'; // Import flutter_hooks
import 'package:hooks_riverpod/hooks_riverpod.dart'; // Use hooks_riverpod
import 'package:ionicons/ionicons.dart';
import 'package:just_audio/just_audio.dart';

import '../../../common/universal_image.dart';
import '../../../core/logging/app_logger.dart';
import '../../../data/models/content_model.dart';
import '../../user_data/application/media_providers.dart';
import '../application/audio_player_provider.dart';

// Changed from ConsumerStatefulWidget to HookConsumerWidget
class AudioPlayerWidget extends HookConsumerWidget {
  const AudioPlayerWidget({
    required this.contentId,
    this.thumbnailUrl,
    super.key,
  });

  final int contentId;
  final String? thumbnailUrl;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get the AudioPlayer instance from Riverpod. It's already disposed by the provider.
    final AudioPlayer audioPlayer = ref.watch(audioPlayerProvider(contentId));

    // Use useEffect to handle initialization of the audio source
    useEffect(() {
      Future<void> initializeAudioSource() async {
        try {
          final ContentModel content =
              await ref.read(contentDetailsProvider(contentId).future);
          final String? url = content.audioData?.audioUrl;

          if (url != null && url.isNotEmpty) {
            // Set the URL. The player's StreamBuilder will react.
            // Await to ensure the source is set before other actions,
            // but handle potential interruptions gracefully.
            await audioPlayer.setUrl(url);
          }
        } catch (e, s) {
          AppLogger.error('Audio Player Source Init Failed',
              error: e, stackTrace: s);
        }
      }

      // Re-run this effect if contentId or the audioPlayer instance changes
      initializeAudioSource();

      // No explicit cleanup needed here as audioPlayer is managed by its provider (ref.onDispose)
      return null;
    }, <Object?>[contentId, audioPlayer]);

    void showSpeedDialog() {
      showDialog(
        context: context,
        builder: (BuildContext context) => AlertDialog(
          title: const Text('اختر سرعة التشغيل'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: <double>[0.5, 0.75, 1.0, 1.25, 1.5, 2.0]
                .map((double speed) => ListTile(
                      title: Text('${speed}x'),
                      onTap: () {
                        audioPlayer.setSpeed(speed);
                        Navigator.of(context).pop();
                      },
                    ))
                .toList(),
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(17.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          if (thumbnailUrl != null)
            ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child:
                  UniversalImage(path: thumbnailUrl!, width: 250, height: 250),
            )
          else
            Container(
              width: 250,
              height: 250,
              decoration: BoxDecoration(
                color: Theme.of(context)
                    .colorScheme
                    .surfaceContainerHighest
                    .withOpacity(0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child:
                  const Icon(Icons.audiotrack, size: 80, color: Colors.white54),
            ),

          const SizedBox(height: 10),
          // Main Play/Pause Button
          StreamBuilder<PlayerState>(
            stream: audioPlayer.playerStateStream,
            builder:
                (BuildContext context, AsyncSnapshot<PlayerState> snapshot) {
              final PlayerState? playerState = snapshot.data;
              final ProcessingState? processingState =
                  playerState?.processingState;
              final bool isPlaying = playerState?.playing ?? false;

              if (processingState == ProcessingState.loading ||
                  processingState == ProcessingState.buffering) {
                return const CircularProgressIndicator();
              }
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  StreamBuilder<LoopMode>(
                    stream: audioPlayer.loopModeStream,
                    builder: (BuildContext context,
                        AsyncSnapshot<LoopMode> snapshot) {
                      final LoopMode loopMode = snapshot.data ?? LoopMode.off;
                      return IconButton(
                        icon: Icon(
                          loopMode == LoopMode.one
                              ? Icons.repeat_one
                              : Icons.repeat,
                          color: loopMode != LoopMode.off
                              ? Theme.of(context).colorScheme.primary
                              : Colors.grey,
                        ),
                        onPressed: () {
                          audioPlayer.setLoopMode(
                            loopMode == LoopMode.off
                                ? LoopMode.one
                                : LoopMode.off,
                          );
                        },
                      );
                    },
                  ),
                  IconButton(
                    icon: const Icon(Ionicons.play_back_outline),
                    iconSize: 32,
                    onPressed: () => audioPlayer.seek(
                        audioPlayer.position - const Duration(seconds: 10)),
                  ),
                  IconButton(
                    icon: Icon(isPlaying
                        ? Icons.pause_circle_filled
                        : Icons.play_circle_filled),
                    iconSize: 64,
                    onPressed: () {
                      isPlaying ? audioPlayer.pause() : audioPlayer.play();
                    },
                  ),
                  IconButton(
                    icon: const Icon(Ionicons.play_forward_outline),
                    iconSize: 32,
                    onPressed: () => audioPlayer.seek(
                        audioPlayer.position + const Duration(seconds: 10)),
                  ),
                  StreamBuilder<double>(
                    stream: audioPlayer.speedStream,
                    builder:
                        (BuildContext context, AsyncSnapshot<double> snapshot) {
                      return TextButton(
                        onPressed: showSpeedDialog,
                        child: Text('${snapshot.data ?? 1.0}x'),
                      );
                    },
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 20),
          StreamBuilder<Duration?>(
            stream: audioPlayer.positionStream,
            builder: (BuildContext context, AsyncSnapshot<Duration?> snapshot) {
              return ProgressBar(
                progress: snapshot.data ?? Duration.zero,
                total: audioPlayer.duration ?? Duration.zero,
                onSeek: audioPlayer.seek,
              );
            },
          ),
        ],
      ),
    );
  }
}
