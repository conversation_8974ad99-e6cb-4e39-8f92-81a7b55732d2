// lib/features/media_player/presentation/enhanced_video_player.dart
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart'; // Import flutter_hooks
import 'package:hooks_riverpod/hooks_riverpod.dart'; // Import hooks_riverpod
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../data/models/content_model.dart';
// Removed unused import: '../../../constants/colors.dart'; // For kPrimaryLight if needed, though prefer theme

class EnhancedVideoPlayer extends HookConsumerWidget {
  const EnhancedVideoPlayer({
    super.key,
    required this.content,
    this.autoPlay = true,
  });

  final ContentModel content;
  final bool autoPlay;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final String? videoId = YoutubePlayer.convertUrlToId(
          content.videoData?.youtubeVideoId ?? '',
        ) ??
        content.videoData?.youtubeVideoId;

    // Use useMemoized to create and manage the YoutubePlayerController
    // Fixed: Explicitly return null if videoId is null
    final YoutubePlayerController? controller = useMemoized(() {
      if (videoId == null) {
        return null; // Return null explicitly if no videoId
      }

      return YoutubePlayerController(
        initialVideoId: videoId,
        flags: YoutubePlayerFlags(
          loop: true,
          forceHD: true,
          hideControls: true, // You might want custom controls
          autoPlay: autoPlay,
        ),
      );
    }, <Object?>[videoId, autoPlay]); // Recreate if videoId or autoPlay changes

    // Use useState for _isPlaying. This will manage the local state for the overlay.
    final ValueNotifier<bool> isPlaying = useState<bool>(autoPlay);

    // Use useEffect to manage controller listeners and disposal
    useEffect(() {
      if (controller == null) {
        return null; // No controller, no effect
      }

      // Listener to update the local isPlaying state when the controller's play state changes
      void playerListener() {
        if (isPlaying.value != controller.value.isPlaying) {
          isPlaying.value = controller.value.isPlaying;
        }
      }

      controller.addListener(playerListener);

      // Cleanup function: remove listener and dispose controller when the effect is re-run or widget unmounts
      return () {
        controller.removeListener(playerListener);
        controller.dispose();
      };
    }, <Object?>[
      controller
    ]); // Re-run effect only if the controller instance changes

    // Handle null videoId or controller early
    if (videoId == null || controller == null) {
      return const Center(
        child: Text(
          'فيديو غير متوفر',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return GestureDetector(
      onTap: () {
        if (controller.value.isPlaying) {
          controller.pause();
        } else {
          controller.play();
        }
      },
      child: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          YoutubePlayer(
            controller: controller,
            showVideoProgressIndicator: true,
            progressIndicatorColor: Theme.of(context).colorScheme.primary,
            aspectRatio: 9 / 16,
            bottomActions: const <Widget>[], // Hide bottom controls
          ),
          AnimatedOpacity(
            // Use ValueNotifier's value to control opacity
            opacity: isPlaying.value ? 0.0 : 1.0,
            duration: const Duration(milliseconds: 300),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.4), // Fixed withOpacity

                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.play_arrow,
                color: Colors.white,
                size: 80.0,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
