// lib/features/media_player/presentation/enhanced_video_player.dart
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart'; // Import flutter_hooks
import 'package:hooks_riverpod/hooks_riverpod.dart'; // Import hooks_riverpod
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../data/models/content_model.dart';
// Removed unused import: '../../../constants/colors.dart'; // For kPrimaryLight if needed, though prefer theme

class EnhancedVideoPlayer extends HookConsumerWidget {
  const EnhancedVideoPlayer({
    super.key,
    required this.content,
    this.autoPlay = true,
    this.onControllerCreated,
  });

  final ContentModel content;
  final bool autoPlay;
  final void Function(YoutubePlayerController?)? onControllerCreated;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final String? videoId = YoutubePlayer.convertUrlToId(
          content.videoData?.youtubeVideoId ?? '',
        ) ??
        content.videoData?.youtubeVideoId;

    // Use useMemoized to create and manage the YoutubePlayerController
    // Fixed: Explicitly return null if videoId is null
    final YoutubePlayerController? controller = useMemoized(() {
      if (videoId == null) {
        return null; // Return null explicitly if no videoId
      }

      return YoutubePlayerController(
        initialVideoId: videoId,
        flags: YoutubePlayerFlags(
          loop: true,
          forceHD: true,
          hideControls: true, // You might want custom controls
          autoPlay: autoPlay,
        ),
      );
    }, <Object?>[videoId, autoPlay]); // Recreate if videoId or autoPlay changes

    // Use useState for _isPlaying. This will manage the local state for the overlay.
    final ValueNotifier<bool> isPlaying = useState<bool>(autoPlay);

    // Use useState for loading state
    final ValueNotifier<bool> isLoading = useState<bool>(true);

    // Use useEffect to manage controller listeners and disposal
    useEffect(() {
      if (controller == null) {
        return null; // No controller, no effect
      }

      // Set loading state based on controller readiness
      // If controller is already ready, don't show loading
      if (controller.value.isReady) {
        isLoading.value = false;
      } else {
        isLoading.value = true;
      }

      // Notify parent about controller creation
      if (onControllerCreated != null) {
        onControllerCreated!(controller);
      }

      // Listener to update the local isPlaying state when the controller's play state changes
      void playerListener() {
        if (isPlaying.value != controller.value.isPlaying) {
          isPlaying.value = controller.value.isPlaying;
        }

        // Update loading state based on player state
        if (controller.value.isReady && isLoading.value) {
          isLoading.value = false;
        }
      }

      controller.addListener(playerListener);

      // Check if already ready (in case the listener is added after ready state)
      if (controller.value.isReady) {
        isLoading.value = false;
      }

      // Cleanup function: remove listener and dispose controller when the effect is re-run or widget unmounts
      return () {
        controller.removeListener(playerListener);
        controller.dispose();
      };
    }, <Object?>[
      controller
    ]); // Re-run effect only if the controller instance changes

    // Handle null videoId or controller early
    if (videoId == null || controller == null) {
      return const Center(
        child: Text(
          'فيديو غير متوفر',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return GestureDetector(
      onTap: () {
        // Don't allow interaction while loading
        if (isLoading.value) {
          return;
        }

        if (controller.value.isPlaying) {
          controller.pause();
        } else {
          controller.play();
        }
      },
      child: Stack(
        alignment: Alignment.center,
        children: <Widget>[
          YoutubePlayer(
            controller: controller,
            showVideoProgressIndicator: true,
            progressIndicatorColor: Theme.of(context).colorScheme.primary,
            aspectRatio: 9 / 16,
            bottomActions: const <Widget>[], // Hide bottom controls
          ),
          // Show loading indicator while video is loading
          if (isLoading.value)
            Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.4),
                shape: BoxShape.circle,
              ),
              padding: const EdgeInsets.all(20),
              child: const CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 3,
              ),
            )
          else
            // Show play button when video is ready and not playing
            AnimatedOpacity(
              // Use ValueNotifier's value to control opacity
              opacity: isPlaying.value ? 0.0 : 1.0,
              duration: const Duration(milliseconds: 300),
              child: Container(
                decoration: BoxDecoration(
                  color:
                      Colors.black.withValues(alpha: 0.4), // Fixed withOpacity
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.play_arrow,
                  color: Colors.white,
                  size: 80.0,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
