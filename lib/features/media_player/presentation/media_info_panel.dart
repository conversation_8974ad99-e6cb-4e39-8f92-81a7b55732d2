import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';

import '../../../data/models/content_model.dart';
import '../../home/<USER>/media_card_components.dart';
import '../../surah_player/presentation/reading_view.dart';

class MediaInfoPanel extends ConsumerWidget {
  const MediaInfoPanel({
    required this.contentItem,
    super.key,
  });

  final ContentModel contentItem;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // --- Title Section ---
            Text(
              contentItem.title,
              style: Theme.of(context)
                  .textTheme
                  .titleMedium
                  ?.copyWith(fontWeight: FontWeight.bold),
            ),
            const Gap(20),
            if (contentItem.description != null &&
                contentItem.description!.isNotEmpty)
              ExpandableMediaDescription(
                  description: contentItem.description!,
                  maxLines: 5,
                  onReadMorePressed: () async {
                    await showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      backgroundColor: Colors.transparent,
                      builder: (_) => DraggableScrollableSheet(
                        initialChildSize: 0.9,
                        builder: (_, ScrollController scrollController) =>
                            ReadingView(
                          mediaId: contentItem.id,
                          scrollController: scrollController,
                        ),
                      ),
                    );
                  }),
            // Padding(
            //   padding: const EdgeInsets.only(top: 8.0),
            //   child: Text(
            //     contentItem.description!,
            //     style: Theme.of(context).textTheme.bodySmall,
            //   ),
            // ),
            const Divider(height: 24),

            // --- Media Details Section (Restored) ---
            // Column(
            //   children: <Widget>[
            //     _buildInfoRow(
            //       context,
            //       'النوع',
            //       contentItem.contentTypeLabel,
            //       Ionicons.pricetag_outline,
            //     ),
            //     // Find and display the category name from the categories provider
            //     categoriesAsync.when(
            //       data: (List<CategoryModel> categories) {
            //         final String categoryName =
            //             _findCategoryName(categories, contentItem.categoryId) ??
            //                 'غير مصنف';
            //         return _buildInfoRow(context, 'التصنيف', categoryName,
            //             Icons.folder_open_outlined);
            //       },
            //       loading: () => _buildInfoRow(
            //           context, 'التصنيف', '...', Icons.folder_open_outlined),
            //       error: (Object e, StackTrace s) => _buildInfoRow(
            //           context, 'التصنيف', 'Error', Icons.error_outline),
            //     ),
            //     if (_getDuration() != null)
            //       _buildInfoRow(
            //         context,
            //         'المدة',
            //         formatDuration(_parseDuration(_getDuration())),
            //         Ionicons.timer_outline,
            //       ),
            //     _buildInfoRow(
            //       context,
            //       'تاريخ النشر',
            //       DateFormat('d MMMM yyyy', 'ar').format(contentItem.createdAt),
            //       Ionicons.calendar_outline,
            //     ),
            //   ],
            // ),

            // const Divider(height: 24),

            // // --- Available Options Section (Restored) ---
            // Text(
            //   'الخيارات المتاحة',
            //   style: Theme.of(context).textTheme.titleMedium?.copyWith(
            //         fontWeight: FontWeight.bold,
            //       ),
            // ),
            // const SizedBox(height: 12),
            // _buildOptionButtons(context, contentItem),
          ],
        ),
      ),
    );
  }
}
