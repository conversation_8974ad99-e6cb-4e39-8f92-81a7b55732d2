import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart' as ytp;

import '../../../common/widgets/category_app_bar.dart';
import '../../../data/models/content_model.dart';
import '../../../routing/app_router.dart';
import '../../user_data/application/media_providers.dart';
import '../widgets/player_action_bar.dart';
import 'audio_player_widget.dart';
import 'media_info_panel.dart';

class MediaPlayerScreen extends ConsumerWidget {
  const MediaPlayerScreen({required this.contentId, super.key});
  final int contentId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<ContentModel> mediaItemAsync =
        ref.watch(contentDetailsProvider(contentId));

    return mediaItemAsync.when(
      data: (ContentModel mediaItem) {
        // Route to the appropriate screen based on content type
        switch (mediaItem.contentType) {
          case 'book':
            // Use WidgetsBinding to avoid calling context navigation during a build
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (context.mounted) {
                context.pushReplacementNamed(
                  SGRoute.pdfInfo.name,
                  pathParameters: <String, String>{'id': contentId.toString()},
                );
              }
            });
            return const Scaffold(
                body: Center(child: CircularProgressIndicator()));
          case 'article':
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (context.mounted) {
                context.pushReplacementNamed(
                  SGRoute.textMediaViewer.name,
                  pathParameters: <String, String>{'id': contentId.toString()},
                );
              }
            });
            return const Scaffold(
                body: Center(child: CircularProgressIndicator()));
          case 'post':
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (context.mounted) {
                context.pushReplacementNamed(
                  SGRoute.tweetDetails.name,
                  pathParameters: <String, String>{'id': contentId.toString()},
                );
              }
            });
            return const Scaffold(
                body: Center(child: CircularProgressIndicator()));
          case 'audio':
          case 'video':
            // Only audio and video are handled by this view
            return _MediaPlayerView(contentItem: mediaItem);
          default:
            return Scaffold(
              appBar: AppBar(),
              body: Center(
                  child: Text('نوع محتوى غير مدعوم: ${mediaItem.contentType}')),
            );
        }
      },
      loading: () => Scaffold(
        appBar: AppBar(),
        body: const Center(child: CircularProgressIndicator()),
      ),
      error: (Object err, StackTrace stack) => Scaffold(
        appBar: AppBar(title: const Text('خطأ')),
        body: Center(child: Text('فشل تحميل المحتوى: $err')),
      ),
    );
  }
}

class _MediaPlayerView extends ConsumerStatefulWidget {
  const _MediaPlayerView({required this.contentItem});
  final ContentModel contentItem;

  @override
  ConsumerState<_MediaPlayerView> createState() => _MediaPlayerViewState();
}

class _MediaPlayerViewState extends ConsumerState<_MediaPlayerView> {
  ytp.YoutubePlayerController? _videoController;

  @override
  void initState() {
    super.initState();
    if (widget.contentItem.contentType == 'video' &&
        widget.contentItem.videoData?.youtubeVideoId != null) {
      _videoController = ytp.YoutubePlayerController(
        initialVideoId: widget.contentItem.videoData!.youtubeVideoId,
        flags: const ytp.YoutubePlayerFlags(
          autoPlay: false,
          forceHD: true,
          // We let the builder handle controls, which is more stable
        ),
      );
    }
  }

  @override
  void dispose() {
    _videoController?.dispose();
    // The audio player is now managed by its own provider and disposed automatically
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CategoryAppBar(
        contentId: widget.contentItem.id,
        title: widget.contentItem.title,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            if (widget.contentItem.contentType == 'video' &&
                _videoController != null)
              // THE FIX: Wrap the player in a YoutubePlayerBuilder
              ytp.YoutubePlayerBuilder(
                player: ytp.YoutubePlayer(
                  controller: _videoController!,
                  showVideoProgressIndicator: true,
                  progressIndicatorColor: Theme.of(context).colorScheme.primary,
                ),
                builder: (BuildContext context, Widget player) {
                  return Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: player,
                    ),
                  );
                },
              ),
            if (widget.contentItem.contentType == 'audio')
              AudioPlayerWidget(
                contentId: widget.contentItem.id,
                thumbnailUrl: widget.contentItem.audioData?.imageUrl,
              ),
            PlayerActionBar(item: widget.contentItem),
            MediaInfoPanel(contentItem: widget.contentItem),
          ],
        ),
      ),
    );
  }
}
