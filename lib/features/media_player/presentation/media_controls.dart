import 'package:audio_video_progress_bar/audio_video_progress_bar.dart';
import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:just_audio/just_audio.dart';

class MediaControls extends StatelessWidget {
  const MediaControls({super.key, required this.audioPlayer});

  final AudioPlayer audioPlayer;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: StreamBuilder<PlayerState>(
        stream: audioPlayer.playerStateStream,
        builder: (BuildContext context, AsyncSnapshot<PlayerState> snapshot) {
          final PlayerState? playerState = snapshot.data;
          final bool isPlaying = playerState?.playing ?? false;
          final ProcessingState? processingState = playerState?.processingState;

          return Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              StreamBuilder<Duration>(
                stream: audioPlayer.positionStream,
                builder:
                    (BuildContext context, AsyncSnapshot<Duration> snapshot) {
                  return ProgressBar(
                    progress: snapshot.data ?? Duration.zero,
                    total: audioPlayer.duration ?? Duration.zero,
                    onSeek: audioPlayer.seek,
                  );
                },
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: <Widget>[
                  IconButton(
                    icon: const Icon(Ionicons.play_back_outline),
                    iconSize: 28,
                    onPressed: () => audioPlayer.seek(
                        audioPlayer.position - const Duration(seconds: 10)),
                  ),
                  if (processingState == ProcessingState.loading ||
                      processingState == ProcessingState.buffering)
                    const CircularProgressIndicator()
                  else
                    IconButton(
                      icon: Icon(isPlaying
                          ? Ionicons.pause_circle
                          : Ionicons.play_circle),
                      iconSize: 54,
                      color: Theme.of(context).colorScheme.primary,
                      onPressed:
                          isPlaying ? audioPlayer.pause : audioPlayer.play,
                    ),
                  IconButton(
                    icon: const Icon(Ionicons.play_forward_outline),
                    iconSize: 28,
                    onPressed: () => audioPlayer.seek(
                        audioPlayer.position + const Duration(seconds: 10)),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }
}
