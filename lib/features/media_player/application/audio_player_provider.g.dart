// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'audio_player_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(audioPlayer)
const audioPlayerProvider = AudioPlayerFamily._();

final class AudioPlayerProvider
    extends $FunctionalProvider<AudioPlayer, AudioPlayer, AudioPlayer>
    with $Provider<AudioPlayer> {
  const AudioPlayerProvider._(
      {required AudioPlayerFamily super.from, required int super.argument})
      : super(
          retry: null,
          name: r'audioPlayerProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$audioPlayerHash();

  @override
  String toString() {
    return r'audioPlayerProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $ProviderElement<AudioPlayer> $createElement($ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  AudioPlayer create(Ref ref) {
    final argument = this.argument as int;
    return audioPlayer(
      ref,
      argument,
    );
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(AudioPlayer value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<AudioPlayer>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is AudioPlayerProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$audioPlayerHash() => r'e641235299c351131ecc2b418a605462e6328ffc';

final class AudioPlayerFamily extends $Family
    with $FunctionalFamilyOverride<AudioPlayer, int> {
  const AudioPlayerFamily._()
      : super(
          retry: null,
          name: r'audioPlayerProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  AudioPlayerProvider call(
    int contentId,
  ) =>
      AudioPlayerProvider._(argument: contentId, from: this);

  @override
  String toString() => r'audioPlayerProvider';
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
