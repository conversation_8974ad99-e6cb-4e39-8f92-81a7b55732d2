// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_player_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(MediaPlayerController)
const mediaPlayerControllerProvider = MediaPlayerControllerFamily._();

final class MediaPlayerControllerProvider
    extends $AsyncNotifierProvider<MediaPlayerController, MediaPlayerState> {
  const MediaPlayerControllerProvider._(
      {required MediaPlayerControllerFamily super.from,
      required int super.argument})
      : super(
          retry: null,
          name: r'mediaPlayerControllerProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaPlayerControllerHash();

  @override
  String toString() {
    return r'mediaPlayerControllerProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  MediaPlayerController create() => MediaPlayerController();

  @override
  bool operator ==(Object other) {
    return other is MediaPlayerControllerProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$mediaPlayerControllerHash() =>
    r'ead1891341e569b40b7ec1d9791bbdb4375d2540';

final class MediaPlayerControllerFamily extends $Family
    with
        $ClassFamilyOverride<
            MediaPlayerController,
            AsyncValue<MediaPlayerState>,
            MediaPlayerState,
            FutureOr<MediaPlayerState>,
            int> {
  const MediaPlayerControllerFamily._()
      : super(
          retry: null,
          name: r'mediaPlayerControllerProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  MediaPlayerControllerProvider call(
    int contentId,
  ) =>
      MediaPlayerControllerProvider._(argument: contentId, from: this);

  @override
  String toString() => r'mediaPlayerControllerProvider';
}

abstract class _$MediaPlayerController
    extends $AsyncNotifier<MediaPlayerState> {
  late final _$args = ref.$arg as int;
  int get contentId => _$args;

  FutureOr<MediaPlayerState> build(
    int contentId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref =
        this.ref as $Ref<AsyncValue<MediaPlayerState>, MediaPlayerState>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<MediaPlayerState>, MediaPlayerState>,
        AsyncValue<MediaPlayerState>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
