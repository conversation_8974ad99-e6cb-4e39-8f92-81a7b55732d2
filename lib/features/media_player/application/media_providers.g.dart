// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(categories)
const categoriesProvider = CategoriesProvider._();

final class CategoriesProvider extends $FunctionalProvider<
        AsyncValue<List<CategoryModel>>,
        List<CategoryModel>,
        FutureOr<List<CategoryModel>>>
    with
        $FutureModifier<List<CategoryModel>>,
        $FutureProvider<List<CategoryModel>> {
  const CategoriesProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'categoriesProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$categoriesHash();

  @$internal
  @override
  $FutureProviderElement<List<CategoryModel>> $createElement(
          $ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<List<CategoryModel>> create(Ref ref) {
    return categories(ref);
  }
}

String _$categoriesHash() => r'2acfa0976121b513757b19d3c15b651129acd676';

@ProviderFor(contentDetails)
const contentDetailsProvider = ContentDetailsFamily._();

final class ContentDetailsProvider extends $FunctionalProvider<
        AsyncValue<ContentModel>, ContentModel, FutureOr<ContentModel>>
    with $FutureModifier<ContentModel>, $FutureProvider<ContentModel> {
  const ContentDetailsProvider._(
      {required ContentDetailsFamily super.from, required int super.argument})
      : super(
          retry: null,
          name: r'contentDetailsProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$contentDetailsHash();

  @override
  String toString() {
    return r'contentDetailsProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $FutureProviderElement<ContentModel> $createElement(
          $ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<ContentModel> create(Ref ref) {
    final argument = this.argument as int;
    return contentDetails(
      ref,
      argument,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is ContentDetailsProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$contentDetailsHash() => r'fc47178d90a877ffff8ada2caea5171e6b72fff5';

final class ContentDetailsFamily extends $Family
    with $FunctionalFamilyOverride<FutureOr<ContentModel>, int> {
  const ContentDetailsFamily._()
      : super(
          retry: null,
          name: r'contentDetailsProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  ContentDetailsProvider call(
    int contentId,
  ) =>
      ContentDetailsProvider._(argument: contentId, from: this);

  @override
  String toString() => r'contentDetailsProvider';
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
