// lib/features/media_player/application/services/interfaces/i_media_download_service.dart
import 'dart:io';

import 'package:dio/dio.dart'; // Import Dio for CancelToken

abstract class IMediaDownloadService {
  Future<String> getDownloadsPath();
  Future<String?> getDownloadedFilePath(String url);
  Future<({bool isSuccess, String? path, String? error, File? fileObject})>
      downloadFile(
    String url,
    String effectiveFileName, {
    void Function(double progress)? onProgress,
    CancelToken? cancelToken, // Add this parameter
  });
}
