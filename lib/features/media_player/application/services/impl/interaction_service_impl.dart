import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../../data/models/content_model.dart';
import '../interfaces/i_interaction_service.dart';

class InteractionServiceImpl implements IInteractionService {
  @override
  Future<void> shareContent(ContentModel mediaItem) async {
    final bool isIos = !kIsWeb && Platform.isIOS;
    final String storeLink = isIos
        ? 'https://apps.apple.com/app/id6747956102'
        : 'https://play.google.com/store/apps/details?id=com.dralfarih.app';

    try {
      final StringBuffer shareText = StringBuffer();
      shareText.writeln(mediaItem.title);
      if (mediaItem.description != null && mediaItem.description!.isNotEmpty) {
        final String shortDesc = mediaItem.description!.length > 150
            ? '${mediaItem.description!.substring(0, 147)}...'
            : mediaItem.description!;
        shareText.writeln('\n$shortDesc');
      }

      shareText.writeln(
        '\n- مشاركة تطبيق الشيخ الفريح رابط التحميل: $storeLink\n\n'
        ' ساهم في نشر التطبيق فالدال على الخير كفاعله',
      );

      final ShareResult result = await SharePlus.instance.share(
        ShareParams(
          text: shareText.toString(),
          subject: mediaItem.title,
        ),
      );

      if (result.status == ShareResultStatus.success) {
        debugPrint('Content shared successfully!');
      }
    } catch (e) {
      debugPrint('خطأ في المشاركة: $e');
      rethrow;
    }
  }

  @override
  Future<void> openInBrowser(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      debugPrint('Could not launch $url in service');
      throw Exception('Could not launch $url');
    }
  }
}
