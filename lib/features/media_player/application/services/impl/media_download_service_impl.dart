// lib/features/media_player/application/services/impl/media_download_service_impl.dart
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart'; // Keep this import if you still want its functionality
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path_helper;
import 'package:path_provider/path_provider.dart';

import '../interfaces/i_media_download_service.dart';

class MediaDownloadServiceImpl implements IMediaDownloadService {
  MediaDownloadServiceImpl({Dio? dio}) : _dio = dio ?? Dio() {
    // It's better to inject Dio as suggested in the previous review.
    // If you always want retry functionality for this specific Dio instance, keep it here.
    // Otherwise, ensure the injected Dio (from NetworkModule) already has the RetryInterceptor.
    if (dio == null) {
      _dio.interceptors.add(RetryInterceptor(dio: _dio, logPrint: debugPrint));
    }
  }
  final Dio _dio;

  @override
  Future<String> getDownloadsPath() async {
    try {
      Directory? directory;
      if (Platform.isAndroid) {
        directory = await getExternalStorageDirectory();
        if (directory != null) {
          final String downloadPath =
              path_helper.join(directory.path, 'Downloads');
          final Directory downloadDir = Directory(downloadPath);
          if (!downloadDir.existsSync()) {
            await downloadDir.create(recursive: true);
          }
          debugPrint('Using Android download path: $downloadPath');
          return downloadPath;
        }
      }
      // Fallback for iOS and other platforms
      directory ??= await getApplicationDocumentsDirectory();
      final String downloadPath = path_helper.join(directory.path, 'Downloads');
      final Directory downloadDir = Directory(downloadPath);
      if (!downloadDir.existsSync()) {
        await downloadDir.create(recursive: true);
      }
      debugPrint('Using Fallback/iOS download path: $downloadPath');
      return downloadPath;
    } catch (e) {
      debugPrint('Error getting downloads path: $e');
      final Directory appDocDir = await getApplicationDocumentsDirectory();
      final String downloadPath = path_helper.join(appDocDir.path, 'Downloads');
      final Directory downloadDir = Directory(downloadPath);
      if (!downloadDir.existsSync()) {
        await downloadDir.create(recursive: true);
      }
      debugPrint('Using final fallback download path: $downloadPath');
      return downloadPath;
    }
  }

  @override
  Future<String?> getDownloadedFilePath(String url) async {
    try {
      final String downloadPath = await getDownloadsPath();
      final String fileName = url.split('/').last;
      final String filePath = path_helper.join(downloadPath, fileName);
      final File file = File(filePath);
      if (file.existsSync()) {
        return filePath;
      }
      return null;
    } catch (e) {
      debugPrint('Error getting downloaded file path: $e');
      return null;
    }
  }

  @override
  Future<({bool isSuccess, String? path, String? error, File? fileObject})>
      downloadFile(
    String url,
    String effectiveFileName, {
    void Function(double progress)? onProgress,
    CancelToken? cancelToken, // Added here
  }) async {
    try {
      final String downloadPath = await getDownloadsPath();
      final String filePath = path_helper.join(downloadPath, effectiveFileName);
      final File file = File(filePath);

      if (file.existsSync()) {
        debugPrint('File already exists at: $filePath');
        return (
          isSuccess: true,
          path: filePath,
          error: 'تم تنزيل الملف مسبقاً',
          fileObject: file
        );
      }

      debugPrint('Downloading from URL: $url');
      debugPrint('Saving to path: $filePath');

      await _dio.download(
        url,
        filePath,
        onReceiveProgress: (int received, int total) {
          if (total != -1 && onProgress != null) {
            final double progress = received / total;
            if (kDebugMode && (progress * 100).round() % 10 == 0) {
              debugPrint(
                  'Download progress: ${(progress * 100).toStringAsFixed(0)}%');
            }
            onProgress(progress);
          }
        },
        cancelToken: cancelToken, // Passed here
      );

      debugPrint('Download completed successfully for: $filePath');
      return (isSuccess: true, path: filePath, error: null, fileObject: file);
    } on DioException catch (e, stacktrace) {
      // Catch DioException specifically
      if (e.type == DioExceptionType.cancel) {
        debugPrint('Download cancelled: $effectiveFileName');
        return (
          isSuccess: false,
          path: null,
          error: 'تم إلغاء التحميل.',
          fileObject: null
        );
      }
      // Log other DioErrors
      // debugPrint('----------------------------------');
      // debugPrint('!!!!!! DOWNLOAD SERVICE ERROR !!!!!!');
      // debugPrint('Error downloading file: $url');
      // debugPrint('Error Type: ${e.runtimeType}');
      // debugPrint('Error Details: $e');
      // debugPrint('DioException Type: ${e.type}');
      // debugPrint('DioException Message: ${e.message}');
      // debugPrint('DioException Response: ${e.response}');
      // debugPrint('Stacktrace: \n$stacktrace');
      // debugPrint('----------------------------------');

      return (
        isSuccess: false,
        path: null,
        error: 'فشل تنزيل الملف. يرجى التحقق من اتصالك وأذونات التخزين.',
        fileObject: null
      );
    } catch (e, stacktrace) {
      // Catch other general errors
      // debugPrint('----------------------------------');
      // debugPrint('!!!!!! DOWNLOAD SERVICE UNKNOWN ERROR !!!!!!');
      // debugPrint('Error downloading file: $url');
      // debugPrint('Error Type: ${e.runtimeType}');
      // debugPrint('Error Details: $e');
      // debugPrint('Stacktrace: \n$stacktrace');
      // debugPrint('----------------------------------');
      return (
        isSuccess: false,
        path: null,
        error: 'حدث خطأ غير متوقع أثناء التحميل.',
        fileObject: null
      );
    }
  }
}
