import 'package:chewie/chewie.dart';
import 'package:just_audio/just_audio.dart';
import 'package:video_player/video_player.dart';

import '../../../../../data/models/content_model.dart';
import '../interfaces/i_media_initialization_service.dart';

class MediaInitializationServiceImpl implements IMediaInitializationService {
  @override
  Future<PlayerInstances> initializePlayer(
    ContentModel mediaItem, {
    Duration startPosition = Duration.zero,
    required void Function(AudioPlayer player, {Duration startPosition})
        setupAudioListeners,
    required void Function(VideoPlayerController controller)
        setupVideoListeners,
  }) async {
    // This service now only initializes audio players.
    if (mediaItem.contentType == 'audio') {
      final String? audioUrl = mediaItem.audioData?.audioUrl;
      if (audioUrl == null || audioUrl.isEmpty) {
        throw Exception('No audio URL found for item: ${mediaItem.title}');
      }

      final AudioPlayer audioPlayer = AudioPlayer();
      setupAudioListeners(audioPlayer, startPosition: startPosition);
      await audioPlayer.setUrl(audioUrl);
      await audioPlayer.load();

      return PlayerInstances(
          audioPlayer: audioPlayer, totalDuration: audioPlayer.duration);
    } else {
      // Videos are handled by EnhancedVideoPlayer, not this service.
      throw UnsupportedError(
          'This service only supports audio initialization.');
    }
  }

  @override
  Future<PlayerInstances> initializePlayerFromFile(
    ContentModel mediaItem,
    String filePath, {
    Duration startPosition = Duration.zero,
    required void Function(AudioPlayer player, {Duration startPosition})
        setupAudioListeners,
    required void Function(VideoPlayerController controller)
        setupVideoListeners,
  }) async {
    if (mediaItem.contentType == 'audio') {
      final AudioPlayer audioPlayer = AudioPlayer();
      setupAudioListeners(audioPlayer, startPosition: startPosition);
      await audioPlayer.setFilePath(filePath);
      await audioPlayer.load();
      return PlayerInstances(
          audioPlayer: audioPlayer, totalDuration: audioPlayer.duration);
    } else {
      throw UnsupportedError(
          'This service only supports audio file initialization.');
    }
  }

  @override
  void disposePlayers({
    AudioPlayer? audioPlayer,
    VideoPlayerController? videoController,
    ChewieController? chewieController,
  }) {
    audioPlayer?.dispose();
    videoController?.dispose();
    chewieController?.dispose();
  }
}
