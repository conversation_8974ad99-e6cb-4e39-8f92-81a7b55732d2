// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(mediaDownloadService)
const mediaDownloadServiceProvider = MediaDownloadServiceProvider._();

final class MediaDownloadServiceProvider extends $FunctionalProvider<
    IMediaDownloadService,
    IMediaDownloadService,
    IMediaDownloadService> with $Provider<IMediaDownloadService> {
  const MediaDownloadServiceProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'mediaDownloadServiceProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaDownloadServiceHash();

  @$internal
  @override
  $ProviderElement<IMediaDownloadService> $createElement(
          $ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  IMediaDownloadService create(Ref ref) {
    return mediaDownloadService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(IMediaDownloadService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<IMediaDownloadService>(value),
    );
  }
}

String _$mediaDownloadServiceHash() =>
    r'a47c574a96ff7f461410e293cffe2da038f72787';

@ProviderFor(mediaInitializationService)
const mediaInitializationServiceProvider =
    MediaInitializationServiceProvider._();

final class MediaInitializationServiceProvider extends $FunctionalProvider<
    IMediaInitializationService,
    IMediaInitializationService,
    IMediaInitializationService> with $Provider<IMediaInitializationService> {
  const MediaInitializationServiceProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'mediaInitializationServiceProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaInitializationServiceHash();

  @$internal
  @override
  $ProviderElement<IMediaInitializationService> $createElement(
          $ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  IMediaInitializationService create(Ref ref) {
    return mediaInitializationService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(IMediaInitializationService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<IMediaInitializationService>(value),
    );
  }
}

String _$mediaInitializationServiceHash() =>
    r'3ecc3c9593dc0e9eddfd5c8d5fce885003035d94';

@ProviderFor(mediaPlaybackService)
const mediaPlaybackServiceProvider = MediaPlaybackServiceProvider._();

final class MediaPlaybackServiceProvider extends $FunctionalProvider<
    IMediaPlaybackService,
    IMediaPlaybackService,
    IMediaPlaybackService> with $Provider<IMediaPlaybackService> {
  const MediaPlaybackServiceProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'mediaPlaybackServiceProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$mediaPlaybackServiceHash();

  @$internal
  @override
  $ProviderElement<IMediaPlaybackService> $createElement(
          $ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  IMediaPlaybackService create(Ref ref) {
    return mediaPlaybackService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(IMediaPlaybackService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<IMediaPlaybackService>(value),
    );
  }
}

String _$mediaPlaybackServiceHash() =>
    r'd69edda786cf77e66c9e5ceb944ad2c1fbde5b4d';

@ProviderFor(pdfDocumentService)
const pdfDocumentServiceProvider = PdfDocumentServiceProvider._();

final class PdfDocumentServiceProvider extends $FunctionalProvider<
    IPdfDocumentService,
    IPdfDocumentService,
    IPdfDocumentService> with $Provider<IPdfDocumentService> {
  const PdfDocumentServiceProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'pdfDocumentServiceProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$pdfDocumentServiceHash();

  @$internal
  @override
  $ProviderElement<IPdfDocumentService> $createElement(
          $ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  IPdfDocumentService create(Ref ref) {
    return pdfDocumentService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(IPdfDocumentService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<IPdfDocumentService>(value),
    );
  }
}

String _$pdfDocumentServiceHash() =>
    r'a035a594dbfd84d6721bd0193263a96a43671c5c';

@ProviderFor(interactionService)
const interactionServiceProvider = InteractionServiceProvider._();

final class InteractionServiceProvider extends $FunctionalProvider<
    IInteractionService,
    IInteractionService,
    IInteractionService> with $Provider<IInteractionService> {
  const InteractionServiceProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'interactionServiceProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$interactionServiceHash();

  @$internal
  @override
  $ProviderElement<IInteractionService> $createElement(
          $ProviderPointer pointer) =>
      $ProviderElement(pointer);

  @override
  IInteractionService create(Ref ref) {
    return interactionService(ref);
  }

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(IInteractionService value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<IInteractionService>(value),
    );
  }
}

String _$interactionServiceHash() =>
    r'd3c20a3974f5a60a883bb6c6e2a3fad87cd8fe28';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
