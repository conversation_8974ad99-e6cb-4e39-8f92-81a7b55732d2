// lib/features/media_player/application/media_player_controller.dart
import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart'; // Import Dio for CancelToken
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/models/content_model.dart';
import '../../../data/models/media_player_state.dart';
import '../../user_data/application/media_providers.dart';
import 'services/service_providers.dart';

part 'media_player_controller.g.dart';

@Riverpod(keepAlive: false)
class MediaPlayerController extends _$MediaPlayerController {
  CancelToken? _downloadCancelToken; // Declare CancelToken

  @override
  Future<MediaPlayerState> build(int contentId) async {
    // Dispose the previous CancelToken if a new build is triggered
    _downloadCancelToken?.cancel();
    _downloadCancelToken = CancelToken();

    // Register a dispose callback for the provider
    ref.onDispose(() {
      _downloadCancelToken?.cancel(); // Cancel download on provider disposal
      _downloadCancelToken = null;
    });

    final ContentModel mediaItem =
        await ref.watch(contentDetailsProvider(contentId).future);

    return MediaPlayerState(
      mediaItem: mediaItem,
      downloadProgress: 0.0,
    );
  }

  Future<({String? error, File? fileObject, bool isSuccess, String? path})>
      downloadMedia() async {
    final ContentModel? mediaItem = state.value?.mediaItem;
    if (mediaItem == null) {
      return (
        isSuccess: false,
        path: null,
        error: 'ملف الوسائط غير موجود.',
        fileObject: null
      );
    }

    final String? url = _getPrimaryUrl(mediaItem);
    if (url == null || url.isEmpty) {
      debugPrint('لايوجد رابط قابل للتحميل لـ ${mediaItem.title}');
      return (
        isSuccess: false,
        path: null,
        error: 'لا يوجد رابط قابل للتحميل.',
        fileObject: null
      );
    }

    state = AsyncData<MediaPlayerState>(
        state.value!.copyWith(isDownloading: true, downloadProgress: 0.0));

    // Pass the cancel token to the download service
    final ({
      String? error,
      File? fileObject,
      bool isSuccess,
      String? path
    }) result = await ref.read(mediaDownloadServiceProvider).downloadFile(
      url,
      _generateCleanFileName(title: mediaItem.title, url: url),
      onProgress: (double progress) {
        if (ref.mounted) {
          state = AsyncData<MediaPlayerState>(
              state.value!.copyWith(downloadProgress: progress));
        }
      },
      cancelToken: _downloadCancelToken, // Passed here
    );

    if (ref.mounted) {
      state = AsyncData<MediaPlayerState>(state.value!.copyWith(
        isDownloading: false,
        downloadedFilePath: result.path,
        errorMessage: result.error,
      ));
      if (result.isSuccess) {}
    }
    return result;
  }

  String? _getPrimaryUrl(ContentModel mediaItem) {
    switch (mediaItem.contentType) {
      case 'audio':
        return mediaItem.audioData?.audioUrl;
      case 'book':
        return mediaItem.bookData?.pdfUrl;
      default:
        return null;
    }
  }

  String _generateCleanFileName({required String title, required String url}) {
    String cleanTitle = title
        .trim()
        .replaceAll(RegExp(r'[<>:"/\\|?*]'), '')
        .replaceAll(' ', '_');
    if (cleanTitle.length > 50) {
      cleanTitle = cleanTitle.substring(0, 50);
    }
    final Uri uri = Uri.parse(url);
    final String fileExtension = uri.pathSegments.last.contains('.')
        ? '.${uri.pathSegments.last.split('.').last}'
        : '.bin';
    return '$cleanTitle$fileExtension';
  }
}
