import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/models/api_response.dart';
import '../../../data/models/category_model.dart';
import '../../../data/models/content_model.dart';
import '../../../data/repository/media_repository.dart';
import '../../../data/repository/media_repository_interface.dart';

part 'media_providers.g.dart';

// Provider to fetch the hierarchical categories
@riverpod
Future<List<CategoryModel>> categories(Ref ref) {
  // Use CategoriesRef
  final MediaRepositoryInterface mediaRepo = ref.watch(mediaRepositoryProvider);
  return mediaRepo.getCategories();
}

// Provider to fetch content for a specific category
Future<PaginatedContentResponse> categoryContents(Ref ref, int categoryId) {
  // Use CategoryContentsRef
  final MediaRepositoryInterface mediaRepo = ref.watch(mediaRepositoryProvider);
  return mediaRepo.getContentsForCategory(categoryId);
}

// Provider to get the details of a single content item by its ID.
@riverpod
Future<ContentModel> contentDetails(Ref ref, int contentId) {
  // Use ContentDetailsRef
  final MediaRepositoryInterface mediaRepo = ref.watch(mediaRepositoryProvider);
  return mediaRepo.getContentById(contentId);
}
