import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:just_audio/just_audio.dart';

import '../../surah_player/presentation/widgets/audio_download_dialog.dart';

class MediaControlBar extends ConsumerWidget {
  const MediaControlBar({
    super.key,
    required this.contentId,
    // It no longer takes a player, but accepts callbacks and state.
    this.onSpeedPressed,
    this.onTimerPressed,
    this.onLoopPressed,
    this.onDownloadPressed,
    this.loopMode = LoopMode.off, // Receives loop mode as a parameter.
  });

  final int contentId;
  final VoidCallback? onSpeedPressed;
  final VoidCallback? onTimerPressed;
  final VoidCallback? onLoopPressed;
  final VoidCallback? onDownloadPressed;
  final LoopMode loopMode;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: <Widget>[
          // Speed Button
          _actionButton(
              context, Ionicons.speedometer_outline, 'السرعة', onSpeedPressed),
          // Timer Button (only for audio)
          if (onTimerPressed != null)
            _actionButton(
                context, Ionicons.timer_outline, 'المؤقت', onTimerPressed),
          // Loop Button
          _actionButton(
            context,
            Ionicons.repeat_outline,
            'تكرار',
            onLoopPressed,
            // The button's color now reflects the state passed into the widget.
            color: loopMode != LoopMode.off
                ? Theme.of(context).colorScheme.primary
                : null,
          ),
          // Download/Library Button
          _actionButton(
            context,
            Ionicons.library_outline,
            'المكتبة',
            // Uses the passed-in callback, defaulting to the audio dialog.
            onDownloadPressed ??
                () => showAudioDownloadDialog(context, contentId),
          ),
        ],
      ),
    );
  }

  Widget _actionButton(BuildContext context, IconData icon, String label,
      VoidCallback? onPressed,
      {Color? color}) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        IconButton(
          icon: Icon(icon,
              color: color ?? Theme.of(context).colorScheme.onSurfaceVariant),
          // The button is disabled if the callback is null.
          onPressed: onPressed,
          iconSize: 24,
        ),
        Text(label,
            style: TextStyle(
                fontSize: 10,
                color: Theme.of(context).colorScheme.onSurfaceVariant)),
      ],
    );
  }
}
