import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';

import '../../../../common/widgets/bookmark_button.dart';
import '../../../../common/widgets/favorite_button.dart';
import '../../../../data/models/content_model.dart';
import '../../../utils/enum_converters.dart';
import '../application/services/service_providers.dart';

/// A styled action bar for the media player screen, matching the Figma design.
class PlayerActionBar extends ConsumerWidget {
  const PlayerActionBar({super.key, required this.item});

  final ContentModel item;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final Color iconColor = colorScheme.primary.withValues(alpha: 0.8);

    return Container(
      height: 48,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: <Widget>[
          // --- Favorite Button (Reusing the existing widget) ---
          FavoriteButton(
            itemId: item.id.toString(),
            itemType: mapContentTypeToEnum(item.contentType),
            color: iconColor,
            activeColor: Colors.redAccent,
          ),

          SizedBox(
              height: 24, child: VerticalDivider(color: Colors.grey.shade300)),

          // --- Bookmark Button (Reusing the existing widget) ---
          BookmarkButton(
            itemId: item.id.toString(),
            itemType: mapContentTypeToEnum(item.contentType),
            color: iconColor,
            activeColor: colorScheme.primary,
          ),
          SizedBox(
              height: 24, child: VerticalDivider(color: Colors.grey.shade300)),

          // --- Share Button ---
          IconButton(
            icon: Icon(Ionicons.share, color: iconColor),
            onPressed: () async {
              await ref.read(interactionServiceProvider).shareContent(item);
            },
          ),
        ],
      ),
    );
  }
}
