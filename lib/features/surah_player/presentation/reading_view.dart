import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:just_audio/just_audio.dart';

import '../../../data/models/content_model.dart';
import '../../user_data/application/media_providers.dart';

class ReadingView extends ConsumerStatefulWidget {
  const ReadingView({
    super.key,
    required this.mediaId,
    this.scrollController,
  });

  final int mediaId;
  final ScrollController? scrollController;

  @override
  ConsumerState<ReadingView> createState() => _ReadingViewState();
}

class _ReadingViewState extends ConsumerState<ReadingView> {
  late final AudioPlayer _audioPlayer;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    final ContentModel content =
        await ref.read(contentDetailsProvider(widget.mediaId).future);
    final String? url = content.audioData?.audioUrl;
    if (url != null && url.isNotEmpty) {
      await _audioPlayer.setAudioSource(
        AudioSource.uri(Uri.parse(url)),
      );
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  /// Get the appropriate text to display based on content type
  String _getDisplayText(ContentModel content) {
    // For audio files, prefer transcript over description
    if (content.contentType == 'audio') {
      final String? transcript = content.audioData?.transcript;
      if (transcript != null && transcript.isNotEmpty) {
        return transcript;
      }
    }

    // Fallback to description for all content types
    return content.description ?? 'لا يوجد نص متاح.';
  }

  @override
  Widget build(BuildContext context) {
    final AsyncValue<ContentModel> mediaAsync =
        ref.watch(contentDetailsProvider(widget.mediaId));

    return mediaAsync.when(
      data: (ContentModel media) {
        if (media == null) {
          return const Center(child: Text('Not found'));
        }
        return ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          child: Scaffold(
            appBar: AppBar(
              automaticallyImplyLeading: false,
              actions: <Widget>[
                IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop())
              ],
            ),
            body: Column(
              children: <Widget>[
                Expanded(
                  child: SingleChildScrollView(
                    controller: widget.scrollController,
                    padding: const EdgeInsets.all(20.0),
                    child: Text(
                      _getDisplayText(media),
                      textDirection: TextDirection.rtl,
                      textAlign: TextAlign.justify,
                      style: Theme.of(context)
                          .textTheme
                          .bodyLarge
                          ?.copyWith(height: 1.8),
                    ),
                  ),
                ),
                // _buildMiniPlayer(context),
                const SizedBox(height: 50),
              ],
            ),
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (Object e, StackTrace s) => Center(child: Text('Error: $e')),
    );
  }
}
