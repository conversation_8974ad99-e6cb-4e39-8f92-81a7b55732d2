import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:just_audio/just_audio.dart';

import '../../../data/models/content_model.dart';
import '../../user_data/application/media_providers.dart';

class ReadingView extends ConsumerStatefulWidget {
  const ReadingView({
    super.key,
    required this.mediaId,
    this.scrollController,
  });

  final int mediaId;
  final ScrollController? scrollController;

  @override
  ConsumerState<ReadingView> createState() => _ReadingViewState();
}

class _ReadingViewState extends ConsumerState<ReadingView> {
  late final AudioPlayer _audioPlayer;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    _initializePlayer();
  }

  Future<void> _initializePlayer() async {
    final ContentModel content =
        await ref.read(contentDetailsProvider(widget.mediaId).future);
    final String? url = content.audioData?.audioUrl;
    if (url != null && url.isNotEmpty) {
      await _audioPlayer.setAudioSource(
        AudioSource.uri(Uri.parse(url)),
      );
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final AsyncValue<ContentModel> surahAsync =
        ref.watch(contentDetailsProvider(widget.mediaId));

    return surahAsync.when(
      data: (ContentModel surah) {
        if (surah == null) {
          return const Center(child: Text('Not found'));
        }
        return ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
          child: Scaffold(
            appBar: AppBar(
              automaticallyImplyLeading: false,
              actions: <Widget>[
                IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.of(context).pop())
              ],
            ),
            body: Column(
              children: <Widget>[
                Expanded(
                  child: SingleChildScrollView(
                    controller: widget.scrollController,
                    padding: const EdgeInsets.all(20.0),
                    child: Text(
                      surah.description ?? 'No text available.',
                      textDirection: TextDirection.rtl,
                      style: Theme.of(context)
                          .textTheme
                          .bodyLarge
                          ?.copyWith(height: 1.8),
                    ),
                  ),
                ),
                // _buildMiniPlayer(context),
              ],
            ),
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (Object e, StackTrace s) => Center(child: Text('Error: $e')),
    );
  }
}
