import 'package:audio_video_progress_bar/audio_video_progress_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart'; // Import flutter_hooks
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart'; // Use hooks_riverpod
import 'package:ionicons/ionicons.dart';
import 'package:just_audio/just_audio.dart';

import '../../../common/universal_image.dart';
import '../../../data/models/content_model.dart';
import '../../../gen/assets.gen.dart';
import '../../user_data/application/media_providers.dart';

// Changed from ConsumerStatefulWidget to HookConsumerWidget
class AudioView extends HookConsumerWidget {
  const AudioView({
    super.key,
    required this.contentId,
  });

  final int contentId;

  /// Get the appropriate text to display based on content type
  String _getDisplayText(ContentModel content) {
    // For audio files, prefer transcript over description
    if (content.contentType == 'audio') {
      final String? transcript = content.audioData?.transcript;
      if (transcript != null && transcript.isNotEmpty) {
        return transcript;
      }
    }

    // Fallback to description for all content types
    return content.description ?? 'وصف صوتي';
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use useMemoized to manage AudioPlayer lifecycle
    final AudioPlayer audioPlayer = useMemoized(() => AudioPlayer());

    // Use useEffect for initialization and disposal logic
    useEffect(() {
      Future<void> initializePlayer() async {
        final ContentModel content =
            await ref.read(contentDetailsProvider(contentId).future);
        final String? url = content.audioData?.audioUrl;
        if (url != null && url.isNotEmpty) {
          try {
            await audioPlayer.setUrl(url);
          } catch (e) {
            debugPrint('Error setting audio source in AudioView: $e');
          }
        }
      }

      initializePlayer();

      return audioPlayer
          .dispose; // Dispose the player when the widget is unmounted
    }, <Object?>[contentId]); // Re-run effect if contentId changes

    final AsyncValue<ContentModel> surahAsync =
        ref.watch(contentDetailsProvider(contentId));
    final double screenHeight = MediaQuery.of(context).size.height;
    final double screenWidth = MediaQuery.of(context).size.width;

    return surahAsync.when(
      data: (ContentModel surah) {
        if (surah == null) {
          return const Center(child: Text('لم يتم تحميل الملف'));
        }
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Column(
                children: <Widget>[
                  MediaTitleBig(screenHeight: screenHeight, surah: surah),
                  const Gap(20),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      children: <Widget>[
                        Card(
                          elevation: 8.0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16.0),
                          ),
                          clipBehavior: Clip.antiAlias,
                          child: UniversalImage(
                            path: surah.audioData?.imageUrl ??
                                Assets.img.drImage.path,
                            width: screenWidth * 0.4,
                            height: screenWidth * 0.4,
                          ),
                        ),
                        const Gap(24),
                      ],
                    ),
                  ),
                ],
              ),
              Gap(screenHeight * 0.05),
              const Gap(24),
              Text(
                surah.title,
                style: Theme.of(context)
                    .textTheme
                    .headlineSmall
                    ?.copyWith(fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const Gap(8),
              Text(
                _getDisplayText(surah),
                style: Theme.of(context)
                    .textTheme
                    .titleMedium
                    ?.copyWith(color: Colors.grey.shade600),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                textAlign: TextAlign.center,
              ),
              const Spacer(),
              _PlayerControls(audioPlayer: audioPlayer),
              const Spacer(),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (Object err, StackTrace stack) => Center(child: Text('خطأ: $err')),
    );
  }
}

/// A dedicated private widget for the playback controls.
class _PlayerControls extends StatelessWidget {
  const _PlayerControls({required this.audioPlayer});
  final AudioPlayer audioPlayer;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<PlayerState>(
      stream: audioPlayer.playerStateStream,
      builder: (BuildContext context, AsyncSnapshot<PlayerState> snapshot) {
        final PlayerState? playerState = snapshot.data;
        final bool isPlaying = playerState?.playing ?? false;
        final ProcessingState? processingState = playerState?.processingState;

        if (processingState == null ||
            processingState == ProcessingState.loading) {
          return const CircularProgressIndicator();
        }

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            StreamBuilder<Duration>(
              stream: audioPlayer.positionStream,
              builder:
                  (BuildContext context, AsyncSnapshot<Duration> snapshot) {
                return ProgressBar(
                  progress: snapshot.data ?? Duration.zero,
                  total: audioPlayer.duration ?? Duration.zero,
                  onSeek: audioPlayer.seek,
                  timeLabelTextStyle: Theme.of(context).textTheme.bodySmall,
                  progressBarColor: Theme.of(context).colorScheme.primary,
                  thumbColor: Theme.of(context).colorScheme.primary,
                );
              },
            ),
            const SizedBox(height: 10),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: <Widget>[
                IconButton(
                  icon: const Icon(Ionicons.play_back_outline),
                  iconSize: 32,
                  onPressed: () => audioPlayer
                      .seek(audioPlayer.position - const Duration(seconds: 10)),
                ),
                IconButton(
                  icon: Icon(
                      isPlaying ? Ionicons.pause_circle : Ionicons.play_circle),
                  iconSize: 64,
                  color: Theme.of(context).colorScheme.primary,
                  onPressed: isPlaying ? audioPlayer.pause : audioPlayer.play,
                ),
                IconButton(
                  icon: const Icon(Ionicons.play_forward_outline),
                  iconSize: 32,
                  onPressed: () => audioPlayer
                      .seek(audioPlayer.position + const Duration(seconds: 10)),
                ),
              ],
            ),
          ],
        );
      },
    );
  }
}

class MediaTitleBig extends StatelessWidget {
  const MediaTitleBig({
    super.key,
    required this.screenHeight,
    required this.surah,
    this.fontSize,
  });

  final double screenHeight;
  final ContentModel? surah;
  final double? fontSize;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: <Widget>[
          Gap(screenHeight * 0.02),
          Text(
            surah!.title,
            style: Theme.of(context)
                .textTheme
                .titleLarge
                ?.copyWith(fontWeight: FontWeight.bold, fontSize: fontSize),
          ),
        ],
      ),
    );
  }
}
