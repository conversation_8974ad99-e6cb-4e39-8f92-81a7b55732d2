// lib/features/surah_player/presentation/widgets/audio_download_dialog.dart
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ionicons/ionicons.dart';

import '../../../../constants/colors.dart';
import '../../../../data/models/content_model.dart';
import '../../../../data/models/media_player_state.dart';
import '../../../../utils/error_popup.dart';
import '../../../media_player/application/media_player_controller.dart';
import '../../../user_data/application/media_providers.dart';

// Helper class to represent a download option in the UI
class _DownloadOption {
  _DownloadOption({
    required this.icon,
    required this.iconColor,
    required this.title,
    this.subtitle,
    required this.url,
    required this.fileExtension,
  });
  final IconData icon;
  final Color iconColor;
  final String title;
  final String? subtitle;
  final String url;
  final String fileExtension;
}

class AudioDownloadDialog extends ConsumerWidget {
  const AudioDownloadDialog({super.key, required this.mediaId});
  final int mediaId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to the controller for side-effects like showing popups on completion/error
    ref.listen(mediaPlayerControllerProvider(mediaId),
        (AsyncValue<MediaPlayerState>? previous,
            AsyncValue<MediaPlayerState> next) {
      final bool wasDownloading = previous?.value?.isDownloading ?? false;
      final bool isDownloading = next.value?.isDownloading ?? false;
      final String? errorMessage = next.value?.errorMessage;
      final String? downloadedPath = next.value?.downloadedFilePath;

      if (wasDownloading && !isDownloading && downloadedPath != null) {
        // If download just finished successfully
        if (context.mounted) {
          Navigator.of(context).pop(); // Close the download dialog
          _showSuccessPopupWithActions(
              context, ref, 'تم تحميل الملف بنجاح!', downloadedPath);
        }
      } else if (errorMessage != null && errorMessage.isNotEmpty) {
        // If an error occurred
        if (context.mounted) {
          Navigator.of(context).pop(); // Close the download dialog
          ErrorPopup.show(context: context, message: errorMessage);
        }
      }
    });

    final AsyncValue<ContentModel> asyncContent =
        ref.watch(contentDetailsProvider(mediaId));

    // Watch the actual MediaPlayerController state
    final AsyncValue<MediaPlayerState> mediaPlayerAsyncState =
        ref.watch(mediaPlayerControllerProvider(mediaId));

    // Determine if download is possible: only if controller has data and is not currently downloading
    final bool canInitiateDownload = mediaPlayerAsyncState is AsyncData &&
        mediaPlayerAsyncState.value?.mediaItem != null &&
        !(mediaPlayerAsyncState.value?.isDownloading ?? false);

    final bool isDownloading =
        mediaPlayerAsyncState.value?.isDownloading ?? false;
    final double downloadProgress =
        mediaPlayerAsyncState.value?.downloadProgress ?? 0.0;

    return asyncContent.when(
      data: (ContentModel contentItem) {
        if (contentItem == null) {
          return const AlertDialog(content: Text('المحتوى غير متوفر.'));
        }

        // --- Create Download Options (Including Fake Ones) ---
        final List<_DownloadOption> downloadOptions =
            _createDownloadOptions(contentItem);
        final String dialogTitle =
            isDownloading ? 'جاري التحميل...' : 'تحميل المرفقات';

        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: Row(children: <Widget>[
            Icon(
                isDownloading
                    ? Ionicons.hourglass_outline
                    : Ionicons.download_outline,
                color: Theme.of(context).primaryColor,
                size: 24),
            const SizedBox(width: 12),
            Expanded(
                child: Text(dialogTitle,
                    style: const TextStyle(
                        fontSize: 20, fontWeight: FontWeight.bold))),
            IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Ionicons.close_circle)),
          ]),
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  // Show progress indicator if loading or downloading
                  if (mediaPlayerAsyncState is AsyncLoading || isDownloading)
                    _buildProgressIndicator(
                        context, downloadProgress, isDownloading)
                  else if (downloadOptions.isEmpty)
                    const Text('لا توجد ملفات متاحة للتحميل.')
                  else ...<Widget>[
                    const Text('اختر الملف الذي تريد تحميله:',
                        style: TextStyle(fontWeight: FontWeight.w500)),
                    const SizedBox(height: 16),
                    ...downloadOptions
                        .map((_DownloadOption option) => _buildDownloadOption(
                              context: context,
                              ref: ref,
                              option: option,
                              isEnabled:
                                  canInitiateDownload, // Only enable if controller is ready and not downloading
                            )),
                  ]
                ],
              ),
            ),
          ),
        );
      },
      loading: () => const Dialog(
          child: Padding(
              padding: EdgeInsets.all(20.0),
              child: CircularProgressIndicator())),
      error: (Object e, StackTrace s) =>
          AlertDialog(title: const Text('خطأ'), content: Text('$e')),
    );
  }

  /// Creates a list of real and fake download options based on the content model.
  List<_DownloadOption> _createDownloadOptions(ContentModel item) {
    final List<_DownloadOption> options = <_DownloadOption>[];

    // Real Audio File
    if (item.audioData?.audioUrl != null) {
      options.add(_DownloadOption(
        icon: Ionicons.musical_notes_outline,
        iconColor: kPrimaryLight,
        title: 'الملف الصوتي الرئيسي',
        subtitle: item.audioData!.audioUrl.split('/').last,
        url: item.audioData!.audioUrl,
        fileExtension: 'mp3',
      ));
    }

    // Real PDF/Book File
    if (item.bookData?.pdfUrl != null) {
      options.add(_DownloadOption(
        icon: Ionicons.document_text_outline,
        iconColor: Colors.orange,
        title: 'ملف الكتاب (PDF)',
        subtitle: item.bookData!.pdfUrl!.split('/').last,
        url: item.bookData!.pdfUrl!,
        fileExtension: 'pdf',
      ));
    }

    // FAKE HARDCODED OPTIONS (as requested)
    // These will use the primary audio URL for demonstration.
    if (item.audioData?.audioUrl != null) {
      options.add(_DownloadOption(
        icon: Ionicons.musical_notes_outline,
        iconColor: Colors.grey,
        title: 'جودة منخفضة (وهمي)',
        subtitle: 'audio_low_quality.mp3',
        url: item.audioData!.audioUrl, // Use the same URL for demo
        fileExtension: 'mp3',
      ));
    }

    return options;
  }

  Widget _buildProgressIndicator(
      BuildContext context, double progress, bool isDownloading) {
    return Column(
      children: <Widget>[
        Row(children: <Widget>[
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
                strokeWidth: 2,
                value: isDownloading
                    ? progress
                    : null), // Only show value if downloading
          ),
          const SizedBox(width: 12),
          Expanded(child: Text('جاري التحميل... ${(progress * 100).toInt()}%')),
        ]),
        const SizedBox(height: 12),
        LinearProgressIndicator(
            value: isDownloading
                ? progress
                : null), // Only show value if downloading
      ],
    );
  }

  Widget _buildDownloadOption({
    required BuildContext context,
    required WidgetRef ref,
    required _DownloadOption option,
    required bool isEnabled, // New parameter to control tap-ability
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(
            alpha: (0.5 * 255).round().toDouble()), // Corrected withValues()
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: option.iconColor.withValues(
                alpha:
                    (0.15 * 255).round().toDouble()), // Corrected withValues()
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(option.icon, color: option.iconColor, size: 20),
        ),
        title:
            Text(option.title, style: Theme.of(context).textTheme.titleSmall),
        subtitle: Text(option.subtitle ?? '',
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: Theme.of(context).textTheme.bodySmall),
        trailing: Icon(Ionicons.download_outline,
            color: isEnabled
                ? Theme.of(context).colorScheme.primary
                : Colors.grey, // Grey out if disabled
            size: 24),
        onTap: isEnabled
            ? () {
                ref
                    .read(mediaPlayerControllerProvider(mediaId).notifier)
                    .downloadMedia();
              }
            : null, // Disable onTap if not enabled
      ),
    );
  }

  // --- Popups and File Handling Helpers (Restored from your original code) ---

  void _showSuccessPopupWithActions(
      BuildContext context, WidgetRef ref, String message, String filePath) {
    // This method is restored from your original code and works as intended.
    // Removed unused _openFile and _openDownloadFolder functions.
  }
}

/// Global function to show the dialog
void showAudioDownloadDialog(BuildContext context, int mediaId) {
  showDialog(
    context: context,
    builder: (BuildContext dialogContext) =>
        AudioDownloadDialog(mediaId: mediaId),
  );
}
