import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ionicons/ionicons.dart';

import '../../../common/widgets/bookmark_button.dart';
import '../../../common/widgets/category_app_bar.dart';
import '../../../common/widgets/favorite_button.dart';
import '../../../data/enums/media_type_enum.dart';
import '../../../data/models/content_model.dart';
import '../../user_data/application/media_providers.dart';
import 'audio_view.dart'; // Assuming this is your main audio player UI
import 'reading_view.dart';

class SurahPlayerScreen extends ConsumerWidget {
  const SurahPlayerScreen({
    super.key,
    required this.surahId,
  });
  // The ID is now an integer
  final int surahId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<ContentModel> surahAsync =
        ref.watch(contentDetailsProvider(surahId));

    return surahAsync.when(
      data: (ContentModel surah) => _SurahPlayerView(surah: surah),
      loading: () => Scaffold(
        appBar: AppBar(),
        body: const Center(child: CircularProgressIndicator()),
      ),
      error: (Object err, StackTrace stack) => Scaffold(
        appBar: AppBar(title: const Text('Error')),
        body: Center(child: Text('Error loading surah: $err')),
      ),
    );
  }
}

class _SurahPlayerView extends StatelessWidget {
  const _SurahPlayerView({required this.surah});
  final ContentModel surah;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CategoryAppBar(
        contentId: surah.id,
        title: surah.title,
        actions: <Widget>[
          FavoriteButton(
              itemId: surah.id.toString(), itemType: MediaType.audio),
          BookmarkButton(
              itemId: surah.id.toString(), itemType: MediaType.audio),
          // Other actions like Share...
        ],
      ),
      body: AudioView(contentId: surah.id),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showReadingViewBottomSheet(context, surah.id),
        label: const Text('قراءة'),
        icon: const Icon(Ionicons.book_outline),
      ),
    );
  }

  Future<void> _showReadingViewBottomSheet(BuildContext context, int surahId) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => DraggableScrollableSheet(
        initialChildSize: 0.9,
        builder: (_, ScrollController scrollController) => ReadingView(
          mediaId: surahId,
          scrollController: scrollController,
        ),
      ),
    );
  }
}
