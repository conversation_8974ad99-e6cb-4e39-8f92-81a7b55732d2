// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'reels_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(reels)
const reelsProvider = ReelsProvider._();

final class ReelsProvider extends $FunctionalProvider<
        AsyncValue<List<ContentModel>>,
        List<ContentModel>,
        FutureOr<List<ContentModel>>>
    with
        $FutureModifier<List<ContentModel>>,
        $FutureProvider<List<ContentModel>> {
  const ReelsProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'reelsProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$reelsHash();

  @$internal
  @override
  $FutureProviderElement<List<ContentModel>> $createElement(
          $ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<List<ContentModel>> create(Ref ref) {
    return reels(ref);
  }
}

String _$reelsHash() => r'40ac6599a8ebd43740d2c69254d47bb0b0b600e5';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
