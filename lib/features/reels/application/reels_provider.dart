import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/models/content_model.dart';
// Import the new response model
import '../../../data/models/reels_response.dart';
import '../../../di/components/service_locator.dart';

part 'reels_provider.g.dart';

// Category ID for short videos
const int shortVideosCategoryId = 33;

@riverpod
Future<List<ContentModel>> reels(Ref ref) async {
  final Dio dio = getIt<Dio>();

  try {
    final Response<Map<String, dynamic>> response = // Explicit type argument
        await dio.get('/categories/$shortVideosCategoryId/contents');

    // Use the new, strongly-typed model to parse the entire response.
    final ReelsContentResponse reelsResponse =
        ReelsContentResponse.fromJson(response.data!);

    // Safely return the strongly-typed list of contents.
    return reelsResponse.data.contents;
  } catch (e) {
    debugPrint('Failed to fetch or parse reels: $e');
    // Return an empty list to prevent the app from crashing on an error.
    return <ContentModel>[];
  }
}
