// lib/features/reels/presentation/reels_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../../../common/widgets/bookmark_button.dart';
import '../../../common/widgets/favorite_button.dart';
import '../../../data/enums/media_type_enum.dart';
import '../../../data/models/content_model.dart';
import '../../../data/repository/media_repository.dart';
import '../../media_player/application/services/service_providers.dart';
import '../../media_player/presentation/enhanced_video_player.dart';
import '../application/reels_provider.dart';

class ReelsScreen extends HookConsumerWidget {
  // Now accepts dynamic type for starting index or content.
  const ReelsScreen({super.key, this.startingIndexOrContent});
  final dynamic startingIndexOrContent;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    SystemChrome.setPreferredOrientations(<DeviceOrientation>[
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    final AsyncValue<List<ContentModel>> reelsAsync = ref.watch(reelsProvider);

    return PopScope(
        onPopInvokedWithResult: (bool didPop, Object? result) {
          // Pause current video when navigating back
          if (didPop) {
            _pauseCurrentVideo();
          }
        },
        child: Scaffold(
          extendBodyBehindAppBar: true,
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
          ),
          body: reelsAsync.when(
            data: (List<ContentModel> reels) {
              if (reels.isEmpty) {
                return const Center(
                  child: Text(
                    'لا توجد مقاطع قصيرة متاحة',
                    style: TextStyle(color: Colors.white),
                  ),
                );
              }
              // Pass the dynamic starting value to the VerticalReelsView
              return VerticalReelsView(
                reels: reels,
                startingIndexOrContent: startingIndexOrContent,
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (Object error, StackTrace stack) => Center(
              child: Text(
                'حدث خطأ: $error',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ),
        ));
  }

  void _pauseCurrentVideo() {
    // Use static reference to pause current video
    _VerticalReelsViewState._instance?.pauseCurrentVideo();
  }
}

class VerticalReelsView extends ConsumerStatefulWidget {
  // Accepts dynamic type for starting index or content.
  const VerticalReelsView({
    super.key,
    required this.reels,
    this.startingIndexOrContent,
  });
  final List<ContentModel> reels;
  final dynamic startingIndexOrContent;

  @override
  ConsumerState<VerticalReelsView> createState() => _VerticalReelsViewState();
}

class _VerticalReelsViewState extends ConsumerState<VerticalReelsView>
    with WidgetsBindingObserver {
  late PageController _pageController;
  late int _currentIndex;
  final Map<int, YoutubePlayerController?> _controllers =
      <int, YoutubePlayerController?>{};
  int? _previousIndex;

  // Static reference for external access
  static _VerticalReelsViewState? _instance;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _instance = this; // Set static reference

    int initialPage = 0;

    if (widget.startingIndexOrContent is int) {
      // If an index is provided directly
      initialPage = widget.startingIndexOrContent as int;
    } else if (widget.startingIndexOrContent is ContentModel) {
      // If a ContentModel is provided, find its index
      final ContentModel targetReel =
          widget.startingIndexOrContent as ContentModel;
      final int foundIndex = widget.reels
          .indexWhere((ContentModel reel) => reel.id == targetReel.id);
      if (foundIndex != -1) {
        initialPage = foundIndex;
      }
    }

    _currentIndex = initialPage;
    _pageController = PageController(initialPage: initialPage);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Pause all videos when app goes to background
    if (state == AppLifecycleState.paused ||
        state == AppLifecycleState.inactive) {
      _pauseAllVideos();
    }
  }

  void _pauseAllVideos() {
    for (final YoutubePlayerController? controller in _controllers.values) {
      if (controller != null) {
        controller.pause();
      }
    }
  }

  void pauseCurrentVideo() {
    if (_controllers[_currentIndex] != null) {
      _controllers[_currentIndex]!.pause();
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _instance = null; // Clear static reference

    // Pause and dispose all video controllers
    for (final YoutubePlayerController? controller in _controllers.values) {
      if (controller != null) {
        controller.pause();
        controller.dispose();
      }
    }
    _controllers.clear();

    _pageController.dispose();
    // Reset orientation when leaving the screen
    SystemChrome.setPreferredOrientations(<DeviceOrientation>[
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    super.dispose();
  }

  void _onControllerCreated(int index, YoutubePlayerController? controller) {
    _controllers[index] = controller;
  }

  void _pausePreviousVideo() {
    if (_previousIndex != null && _controllers[_previousIndex] != null) {
      _controllers[_previousIndex]!.pause();
    }
  }

  @override
  Widget build(BuildContext context) {
    return PageView.builder(
      scrollDirection: Axis.vertical,
      controller: _pageController,
      itemCount: widget.reels.length,
      onPageChanged: (int index) {
        _pausePreviousVideo();
        setState(() {
          _previousIndex = _currentIndex;
          _currentIndex = index;
        });
      },
      itemBuilder: (BuildContext context, int index) {
        final ContentModel reel = widget.reels[index];
        return ReelPage(
          reel: reel,
          isActive: _currentIndex == index,
          onVisitContent: () {
            // Track video view
            ref.read(mediaRepositoryProvider).visitContent(reel.id);
          },
          onControllerCreated: (YoutubePlayerController? controller) {
            _onControllerCreated(index, controller);
          },
        );
      },
    );
  }
}

class ReelPage extends ConsumerStatefulWidget {
  const ReelPage({
    super.key,
    required this.reel,
    required this.isActive,
    required this.onVisitContent,
    this.onControllerCreated,
  });
  final ContentModel reel;
  final bool isActive;
  final VoidCallback onVisitContent;
  final void Function(YoutubePlayerController?)? onControllerCreated;

  @override
  ConsumerState<ReelPage> createState() => _ReelPageState();
}

class _ReelPageState extends ConsumerState<ReelPage>
    with AutomaticKeepAliveClientMixin {
  bool _isLiked = false;

  @override
  void initState() {
    super.initState();
    if (widget.isActive) {
      widget.onVisitContent();
    }
  }

  @override
  void didUpdateWidget(ReelPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive && !oldWidget.isActive) {
      widget.onVisitContent();
    }
  }

  void _toggleLike() {
    setState(() {
      _isLiked = !_isLiked;
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Stack(
      fit: StackFit.expand,
      children: <Widget>[
        // Video Player
        Center(
          child: EnhancedVideoPlayer(
            content: widget.reel,
            autoPlay: widget.isActive,
            onControllerCreated: widget.onControllerCreated,
          ),
        ),

        // Overlay with video info and actions
        _buildOverlay(),
      ],
    );
  }

  Widget _buildOverlay() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // Video title and description
          Text(
            widget.reel.title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              shadows: <Shadow>[Shadow(blurRadius: 4, color: Colors.black54)],
            ),
          ),
          if (widget.reel.description != null)
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                widget.reel.description!,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  shadows: <Shadow>[
                    Shadow(blurRadius: 4, color: Colors.black54)
                  ],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),

          const SizedBox(height: 20),

          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              // Left side - empty or can add more info
              const Spacer(),

              // Right side - action buttons
              Column(
                children: <Widget>[
                  // Like button
                  IconButton(
                    onPressed: _toggleLike,
                    icon: Icon(
                      _isLiked ? Ionicons.heart : Ionicons.heart_outline,
                      color: _isLiked ? Colors.red : Colors.white,
                      size: 30,
                    ),
                  ),
                  const SizedBox(height: 8),

                  // Favorite button
                  FavoriteButton(
                    itemId: widget.reel.id.toString(),
                    itemType: MediaType.video,
                    size: 30,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 8),

                  // Bookmark button
                  BookmarkButton(
                    itemId: widget.reel.id.toString(),
                    itemType: MediaType.video,
                    size: 30,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 8),

                  // Share button
                  IconButton(
                    onPressed: () async {
                      await ref
                          .read(interactionServiceProvider)
                          .shareContent(widget.reel);
                    },
                    icon: const Icon(
                      Ionicons.share,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
