import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';

import '../../../../common/universal_image.dart';
import '../../../../common/widgets/bookmark_button.dart';
import '../../../../common/widgets/favorite_button.dart';
import '../../../../data/enums/media_type_enum.dart';
import '../../../../data/models/content_model.dart';
import '../../../media_player/application/services/service_providers.dart';
import '../../../user_data/application/user_data_providers.dart';

class ReelCard extends ConsumerWidget {
  const ReelCard({
    super.key,
    required this.reel,
  });

  final ContentModel reel;

  void _showActionsBottomSheet(
      BuildContext context, WidgetRef ref, ContentModel reel) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                child: Row(
                  children: <Widget>[
                    ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: UniversalImage(
                        path: reel.videoData?.thumbnailUrl ?? '',
                        width: 60,
                        height: 60 * (16 / 9),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            reel.title,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const Text(
                            'أ.د. محمد بن فهد الفريح',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const Divider(height: 1),
              ListTile(
                leading: FavoriteButton(
                  itemId: reel.id.toString(),
                  itemType: MediaType.video,
                ),
                title: const Text('إضافة إلى المفضلة / إزالة'),
                onTap: () {
                  ref.read(favoritesProvider.notifier).toggleFavorite(
                      itemId: reel.id.toString(), type: MediaType.video);
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: BookmarkButton(
                  itemId: reel.id.toString(),
                  itemType: MediaType.video,
                ),
                title: const Text('حفظ في قائمة المحفوظات'),
                onTap: () {
                  ref.read(bookmarksProvider.notifier).toggleBookmark(
                      itemId: reel.id.toString(), type: MediaType.video);
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Ionicons.share_outline),
                title: const Text('مشاركة'),
                onTap: () async {
                  Navigator.pop(context);
                  await ref.read(interactionServiceProvider).shareContent(reel);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final String hqThumbnailUrl = reel.videoData?.thumbnailUrl
            ?.replaceAll('default.jpg', 'hqdefault.jpg') ??
        '';

    // REMOVED GestureDetector from here. The parent widget (`ReelsGridView`) now handles the tap.
    return Card(
      clipBehavior: Clip.antiAlias,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Stack(
        fit: StackFit.expand,
        children: <Widget>[
          UniversalImage(path: hqThumbnailUrl),
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: <Color>[
                  Colors.black.withValues(alpha: 0.7),
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.7)
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                stops: const <double>[0.0, 0.5, 1.0],
              ),
            ),
          ),
          Positioned(
            bottom: 10,
            left: 10,
            right: 10,
            child: Text(
              reel.title,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Positioned(
            top: 4,
            right: 4,
            child: IconButton(
              icon: const Icon(Icons.more_vert, color: Colors.white),
              onPressed: () => _showActionsBottomSheet(context, ref, reel),
              tooltip: 'خيارات إضافية',
            ),
          ),
        ],
      ),
    );
  }
}
