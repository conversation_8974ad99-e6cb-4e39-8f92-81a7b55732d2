import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../data/models/content_model.dart';
import '../../../../routing/app_router.dart';
import '../../application/reels_provider.dart';
import 'reel_card.dart';

class ReelsGridView extends ConsumerWidget {
  const ReelsGridView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<List<ContentModel>> reelsAsync = ref.watch(reelsProvider);

    return reelsAsync.when(
      data: (List<ContentModel> reels) {
        if (reels.isEmpty) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.all(20.0),
              child: Text('لا توجد مقاطع قصيرة متاحة'),
            ),
          );
        }
        return GridView.builder(
          // These properties prevent the grid from scrolling independently inside the CustomScrollView
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.all(12),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2, // You can adjust the number of columns
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 9 / 16, // Aspect ratio for a reel
          ),
          itemCount: reels.length,
          itemBuilder: (BuildContext context, int index) {
            final ContentModel reel = reels[index];
            // ReelCard's own onTap is ignored here, we wrap it in a GestureDetector
            return GestureDetector(
              onTap: () {
                // Navigate to the ReelsScreen, passing the index of the tapped reel.
                context.push(SGRoute.reels.route, extra: index);
              },
              child: ReelCard(reel: reel),
            );
          },
        );
      },
      loading: () => const Padding(
        padding: EdgeInsets.only(top: 20),
        child: Center(child: CircularProgressIndicator()),
      ),
      error: (Object err, StackTrace stack) => const Center(child: Text('خطأ')),
    );
  }
}
