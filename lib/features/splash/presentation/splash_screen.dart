import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../common/universal_image.dart';
import '../../../constants/colors.dart';
import '../../../gen/assets.gen.dart';

/// Custom splash screen with background and center images
class SplashScreen extends HookConsumerWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use hooks for animations
    final AnimationController fadeController = useAnimationController(
      duration: const Duration(milliseconds: 1500),
    );

    final AnimationController scaleController = useAnimationController(
      duration: const Duration(milliseconds: 2000),
    );

    final Animation<double> fadeAnimation = useMemoized(
        () => Tween<double>(
              begin: 0.0,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: fadeController,
              curve: Curves.easeInOut,
            )),
        <Object?>[fadeController]);

    final Animation<double> scaleAnimation = useMemoized(
        () => Tween<double>(
              begin: 0.8,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: scaleController,
              curve: Curves.elasticOut,
            )),
        <Object?>[scaleController]);

    // Initialize animations and navigation on first build
    useEffect(() {
      // Start animations
      fadeController.forward();
      Future<void>.delayed(const Duration(milliseconds: 300), () {
        scaleController.forward();
      });

      // Navigate to home after delay
      final Timer timer = Timer(const Duration(seconds: 3), () {
        context.go('/home');
      });

      // Set status bar to transparent
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: Colors.transparent,
        ),
      );

      // Cleanup timer on dispose
      return () => timer.cancel();
    }, const <Object?>[]);

    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(Assets.img.splashScreenBackground.path),
            fit: BoxFit.cover,
          ),
        ),
        child: AnimatedBuilder(
          animation:
              Listenable.merge(<Listenable?>[fadeAnimation, scaleAnimation]),
          builder: (BuildContext context, Widget? child) {
            return FadeTransition(
              opacity: fadeAnimation,
              child: Stack(
                children: <Widget>[
                  // Center logo
                  Center(
                    child: ScaleTransition(
                      scale: scaleAnimation,
                      child: Container(
                        width: 400,
                        height: 400,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(150),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(150),
                          child: UniversalImage(
                            path: Assets.img.splashScreenCenter.path,
                            width: 300,
                            height: 300,
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Loading indicator
                  Positioned(
                    bottom: 120,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Column(
                        children: <Widget>[
                          SizedBox(
                            width: 30,
                            height: 30,
                            child: CircularProgressIndicator(
                              strokeWidth: 3,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                kPrimaryLight.withValues(alpha: 0.8),
                              ),
                            ),
                          ),
                          // const SizedBox(height: 16),
                          // Text(
                          //   'جاري تحميل التطبيق...',
                          //   style: TextStyle(
                          //     color: Colors.white.withValues(alpha: 0.9),
                          //     fontSize: 16,
                          //     fontWeight: FontWeight.w500,
                          //   ),
                          // ),
                        ],
                      ),
                    ),
                  ),

                  // App version
                  Positioned(
                    bottom: 40,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Text(
                        'الإصدار 1.0.0',
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.7),
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
