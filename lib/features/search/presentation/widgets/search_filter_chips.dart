import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../data/models/category_model.dart';
import '../../../../data/repository/media_repository.dart';

class SearchFilterChips extends ConsumerWidget {
  const SearchFilterChips({
    super.key,
    required this.selectedCategoryIds,
    required this.onCategorySelected,
  });

  final List<int> selectedCategoryIds;
  final ValueChanged<int> onCategorySelected;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Fetch all categories available for filtering
    final Future<List<CategoryModel>> categoriesAsync =
        ref.watch(mediaRepositoryProvider).getCategories();

    return FutureBuilder<List<CategoryModel>>(
      future: categoriesAsync,
      builder:
          (BuildContext context, AsyncSnapshot<List<CategoryModel>> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator.adaptive());
        }

        if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
          // Don't show anything if categories can't be fetched
          return const SizedBox.shrink();
        }

        final List<CategoryModel> categories = snapshot.data!;

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Wrap(
            spacing: 8.0,
            runSpacing: 4.0,
            children: categories.map((CategoryModel category) {
              final bool isSelected = selectedCategoryIds.contains(category.id);
              return FilterChip(
                label: Text(category.name),
                selected: isSelected,
                onSelected: (_) => onCategorySelected(category.id),
                selectedColor: Theme.of(context).colorScheme.primary.withValues(
                      alpha: 0.2,
                    ),
                checkmarkColor: Theme.of(context).colorScheme.primary,
              );
            }).toList(),
          ),
        );
      },
    );
  }
}
