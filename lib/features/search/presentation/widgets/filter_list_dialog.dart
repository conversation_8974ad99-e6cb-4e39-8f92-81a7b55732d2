import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../data/models/category_model.dart';
import '../../../../data/repository/media_repository.dart';

class FilterListDialog extends StatefulHookConsumerWidget {
  const FilterListDialog({
    super.key,
    required this.onApply,
    required this.selectedCategories,
  });

  final Function(List<int>) onApply;
  final List<int> selectedCategories;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _FilterListDialogState();
}

class _FilterListDialogState extends ConsumerState<FilterListDialog> {
  late List<int> _selectedCategories;

  @override
  void initState() {
    super.initState();
    _selectedCategories = List<int>.from(widget.selectedCategories);
  }

  @override
  Widget build(BuildContext context) {
    // FIX: The provider directly returns a Future, no need for .then or .getOrElse
    final Future<List<CategoryModel>> categoriesFuture =
        ref.watch(mediaRepositoryProvider).getCategories();

    return AlertDialog(
      title: const Text('البحث حسب القسم'),
      content: SizedBox(
        width: double.maxFinite,
        // FIX: The FutureBuilder is now correctly typed
        child: FutureBuilder<List<CategoryModel>>(
          future: categoriesFuture,
          // FIX: The snapshot is now correctly typed
          builder: (BuildContext context,
              AsyncSnapshot<List<CategoryModel>> snapshot) {
            if (snapshot.hasData) {
              // FIX: The data is now a non-nullable, correctly-typed list
              final List<CategoryModel> categories = snapshot.data!;
              if (categories.isEmpty) {
                return const Center(child: Text('لا توجد أقسام'));
              }
              return ListView.builder(
                shrinkWrap: true,
                itemCount: categories.length,
                itemBuilder: (BuildContext context, int index) {
                  // FIX: 'category' is now correctly typed as CategoryModel
                  final CategoryModel category = categories[index];
                  return CheckboxListTile(
                    title: Text(category.name),
                    value: _selectedCategories.contains(category.id),
                    onChanged: (bool? value) {
                      setState(() {
                        if (value ?? true) {
                          _selectedCategories.add(category.id);
                        } else {
                          _selectedCategories.remove(category.id);
                        }
                      });
                    },
                  );
                },
              );
            } else if (snapshot.hasError) {
              return const Center(child: Text('حدث خطأ في تحميل الأقسام'));
            } else {
              return const Center(child: CircularProgressIndicator());
            }
          },
        ),
      ),
      actions: <Widget>[
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        TextButton(
          onPressed: () {
            widget.onApply(_selectedCategories);
            // Navigator.of(context).pop();
          },
          child: const Text('تطبيق'),
        ),
      ],
    );
  }
}
