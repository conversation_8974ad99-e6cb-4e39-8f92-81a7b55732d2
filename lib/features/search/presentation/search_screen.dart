// lib/features/search/presentation/search_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ionicons/ionicons.dart';

import '../../../common/widgets/media_list_item.dart';
import '../../../data/models/api_response.dart';
import '../../../data/models/content_model.dart';
import '../application/search_notifier.dart';
import 'widgets/search_filter_chips.dart';

class SearchScreen extends HookConsumerWidget {
  const SearchScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Hooks to manage the user's input state (text and selected categories)
    final TextEditingController searchController = useTextEditingController();
    final ValueNotifier<List<int>> selectedCategories =
        useState<List<int>>(<int>[]);

    // Watch the state of the search operation
    final AsyncValue<SearchResultsResponse?>
        searchState = // Explicit type annotation
        ref.watch(searchNotifierProvider);
    final ThemeData theme = Theme.of(context);

    // Function to trigger a search.
    void runSearch() {
      // Don't run search if the text field is empty
      if (searchController.text.trim().isNotEmpty) {
        ref.read(searchNotifierProvider.notifier).executeSearch(
              query: searchController.text,
              categoryIds: selectedCategories.value,
            );
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: searchController,
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'ابحث...',
            border: InputBorder.none,
          ),
          style: theme.textTheme.bodyLarge,
          // Trigger search on keyboard action
          onSubmitted: (_) => runSearch(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          children: <Widget>[
            SearchFilterChips(
              selectedCategoryIds: selectedCategories.value,
              onCategorySelected: (int categoryId) {
                final List<int> newSelection =
                    List<int>.from(selectedCategories.value);
                if (newSelection.contains(categoryId)) {
                  newSelection.remove(categoryId);
                } else {
                  newSelection.add(categoryId);
                }
                selectedCategories.value = newSelection;
                // Automatically re-run the search when a filter is changed.
                runSearch();
              },
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: runSearch,
                icon: const Icon(Ionicons.search),
                label: const Text('بحث'),
              ),
            ),
            const Divider(),
            Expanded(
              child: searchState.when(
                data: (SearchResultsResponse? data) {
                  if (data == null) {
                    return _buildInitialState(
                        context); // No search performed yet
                  }
                  if (data.results.isEmpty) {
                    return _buildEmptyState(
                        context); // Search performed, no results
                  }
                  return _buildResultsList(data);
                },
                loading: () =>
                    const Center(child: CircularProgressIndicator.adaptive()),
                error: (Object error, StackTrace stack) => Center(
                    child: Text('حدث خطأ أثناء البحث',
                        style: theme.textTheme.bodyMedium)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInitialState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(
            Ionicons.search_circle_outline,
            size: 80,
            color: Theme.of(context)
                .colorScheme
                .primary
                .withOpacity(0.5), // Fixed .withValues
          ),
          const SizedBox(height: 16),
          Text('ابحث في محتوى الشيخ',
              style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 8),
          Text('اكتب كلمة للبحث ثم اضغط على زر البحث',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Icon(Ionicons.sad_outline,
              size: 80, color: Theme.of(context).textTheme.bodySmall?.color),
          const SizedBox(height: 16),
          Text('لا توجد نتائج',
              style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 8),
          Text('حاول البحث بكلمات مختلفة أو تغيير مرشحات البحث',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center),
        ],
      ),
    );
  }

  // The _navigateToContent helper is no longer needed here,
  // as MediaListItem now handles default navigation internally.
  // void _navigateToContent(BuildContext context, ContentModel item) {
  //   final String contentId = item.id.toString();
  //   context.pushNamed(SGRoute.mediaPlayer.name,
  //       pathParameters: <String, String>{'id': contentId});
  // }

  Widget _buildResultsList(SearchResultsResponse data) {
    return ListView.builder(
      key: ValueKey<String?>(data.searchQuery),
      itemCount: data.results.length,
      itemBuilder: (BuildContext context, int index) {
        final CategorySearchResult categoryResult = data.results[index];
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.fromLTRB(0, 16, 0, 8),
              child: Text(
                categoryResult.category.name,
                style: Theme.of(context)
                    .textTheme
                    .titleMedium
                    ?.copyWith(fontWeight: FontWeight.bold),
              ),
            ),
            ...categoryResult.contents.map(
              (ContentModel content) => MediaListItem(
                item: content,
                // onTap can be left null to use MediaListItem's default navigation.
                // If you need a custom onTap, provide it here.
                // onTap: () => _navigateToContent(context, content),
              ),
            ),
          ],
        );
      },
    );
  }
}
