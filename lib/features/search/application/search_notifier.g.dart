// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_notifier.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(SearchNotifier)
const searchNotifierProvider = SearchNotifierProvider._();

final class SearchNotifierProvider
    extends $AsyncNotifierProvider<SearchNotifier, SearchResultsResponse?> {
  const SearchNotifierProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'searchNotifierProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$searchNotifierHash();

  @$internal
  @override
  SearchNotifier create() => SearchNotifier();
}

String _$searchNotifierHash() => r'd0a3b1f94a5aa3bd558a8769cee4a477853e2d14';

abstract class _$SearchNotifier extends $AsyncNotifier<SearchResultsResponse?> {
  FutureOr<SearchResultsResponse?> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref
        as $Ref<AsyncValue<SearchResultsResponse?>, SearchResultsResponse?>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<SearchResultsResponse?>, SearchResultsResponse?>,
        AsyncValue<SearchResultsResponse?>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
