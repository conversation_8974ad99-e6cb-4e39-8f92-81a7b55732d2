// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'search_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(search)
const searchProvider = SearchFamily._();

final class SearchProvider extends $FunctionalProvider<
        AsyncValue<SearchResultsResponse>,
        SearchResultsResponse,
        FutureOr<SearchResultsResponse>>
    with
        $FutureModifier<SearchResultsResponse>,
        $FutureProvider<SearchResultsResponse> {
  const SearchProvider._(
      {required SearchFamily super.from,
      required SearchParameters super.argument})
      : super(
          retry: null,
          name: r'searchProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$searchHash();

  @override
  String toString() {
    return r'searchProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $FutureProviderElement<SearchResultsResponse> $createElement(
          $ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<SearchResultsResponse> create(Ref ref) {
    final argument = this.argument as SearchParameters;
    return search(
      ref,
      argument,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is SearchProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$searchHash() => r'c0d4fa19f987dfc2a001d7b7a242524f27361615';

final class SearchFamily extends $Family
    with
        $FunctionalFamilyOverride<FutureOr<SearchResultsResponse>,
            SearchParameters> {
  const SearchFamily._()
      : super(
          retry: null,
          name: r'searchProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  SearchProvider call(
    SearchParameters params,
  ) =>
      SearchProvider._(argument: params, from: this);

  @override
  String toString() => r'searchProvider';
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
