import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/models/api_response.dart';
import '../../../data/repository/media_repository.dart';

part 'search_provider.g.dart';

/// A helper class to hold the search parameters.
@immutable
class SearchParameters {
  const SearchParameters(
      {required this.query, this.categoryIds = const <int>[]});
  final String query;
  final List<int> categoryIds;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SearchParameters &&
          runtimeType == other.runtimeType &&
          query == other.query &&
          // Using DeepCollectionEquality for a proper comparison of list contents
          const DeepCollectionEquality().equals(categoryIds, other.categoryIds);

  @override
  int get hashCode =>
      query.hashCode ^ const DeepCollectionEquality().hash(categoryIds);
}

@riverpod
Future<SearchResultsResponse> search(
  Ref ref,
  SearchParameters params,
) async {
  // If the search query is empty, return an empty result immediately
  // without hitting the API.
  if (params.query.trim().isEmpty) {
    return const SearchResultsResponse(
      searchQuery: '',
      results: <CategorySearchResult>[],
      totalResults: 0,
      categoriesWithResults: 0,
    );
  }

  // Use the single, unified search method from the repository.
  // This method correctly handles whether the category list is empty or not.
  final SearchResultsResponse searchResults =
      await ref.watch(mediaRepositoryProvider).search(
            query: params.query,
            categoryIds: params.categoryIds,
          );

  return searchResults;
}
