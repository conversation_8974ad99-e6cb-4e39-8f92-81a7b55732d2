import 'dart:async';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/models/api_response.dart';
import '../../../data/repository/media_repository.dart';
import '../../../data/repository/media_repository_interface.dart';

part 'search_notifier.g.dart';

@riverpod
class SearchNotifier extends _$SearchNotifier {
  @override
  FutureOr<SearchResultsResponse?> build() {
    // Return null initially. This signifies that no search has been performed yet.
    return null;
  }

  /// Executes a search query against the repository.
  /// UI components call this method to trigger a new search.
  Future<void> executeSearch(
      {required String query, List<int>? categoryIds}) async {
    // If the query is empty, reset the state to initial (null).
    if (query.trim().isEmpty) {
      state = const AsyncValue<SearchResultsResponse?>.data(null);
      return;
    }

    // Set the state to loading to show an indicator in the UI.
    state = const AsyncLoading<SearchResultsResponse?>();

    // Execute the search and update the state with the result.
    // AsyncValue.guard will automatically handle success and error states.
    state = await AsyncValue.guard(() {
      final MediaRepositoryInterface repo = ref.read(mediaRepositoryProvider);
      return repo.search(query: query, categoryIds: categoryIds);
    });
  }
}
