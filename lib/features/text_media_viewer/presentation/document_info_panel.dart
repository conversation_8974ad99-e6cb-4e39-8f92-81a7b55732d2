import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';

import '../../../data/models/content_model.dart';

class DocumentInfoPanel extends ConsumerWidget {
  const DocumentInfoPanel({
    required this.contentItem,
    super.key,
  });

  final ContentModel contentItem;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(contentItem.title,
                style: Theme.of(context).textTheme.titleLarge),
            if (contentItem.description != null &&
                contentItem.description!.isNotEmpty) ...<Widget>[
              const SizedBox(height: 8.0),
              Text(contentItem.description!,
                  style: Theme.of(context).textTheme.bodySmall),
            ],
            const Divider(height: 32),
            _buildMetadataSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildMetadataSection(BuildContext context) {
    // Get data directly from the content model
    final String pages = contentItem.bookData?.pages?.toString() ?? 'N/A';
    final String publishedDate =
        DateFormat.yMMMd().format(contentItem.createdAt);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(
          'Details',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        _buildMetadataItem(context, Ionicons.book_outline, 'Pages: $pages'),
        _buildMetadataItem(
            context, Ionicons.calendar_outline, 'Published: $publishedDate'),
        _buildMetadataItem(context, Ionicons.pricetag_outline,
            'Type: ${contentItem.contentTypeLabel}'),
      ],
    );
  }

  Widget _buildMetadataItem(BuildContext context, IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: <Widget>[
          Icon(icon, size: 16.0, color: Theme.of(context).colorScheme.primary),
          const SizedBox(width: 8.0),
          Expanded(
              child: Text(text, style: Theme.of(context).textTheme.bodyMedium)),
        ],
      ),
    );
  }
}
