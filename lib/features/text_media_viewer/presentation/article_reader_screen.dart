import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../common/universal_image.dart';
import '../../../common/widgets/category_app_bar.dart';
import '../../../data/models/content_model.dart';
import '../../media_player/application/services/service_providers.dart';

/// A versatile screen that displays different types of text-based content,
/// such as articles or posts, with a professional and context-aware UI.
class ArticleReaderScreen extends HookConsumerWidget {
  const ArticleReaderScreen({
    required this.mediaItem,
    super.key,
  });

  final ContentModel mediaItem;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // State for UI controls is managed locally with hooks.
    final ValueNotifier<double> fontSize = useState<double>(16.0);
    final ValueNotifier<bool> isDarkMode = useState<bool>(false);
    final ArticleData? articleData = mediaItem.articleData;

    // A theme-aware text style for metadata.
    final TextStyle metadataStyle = TextStyle(
      color: isDarkMode.value ? Colors.grey[400] : Colors.grey[600],
      fontSize: 14,
    );

    return Scaffold(
      appBar: CategoryAppBar(
        contentId: mediaItem.id,
        title: mediaItem.title,
        actions: _buildAppBarActions(fontSize, isDarkMode, ref),
      ),
      backgroundColor:
          isDarkMode.value ? const Color(0xFF1E1E1E) : Colors.white,
      body: (articleData == null || articleData.text.isEmpty)
          ? const Center(child: Text('لا يوجد محتوى لهذا المقال.'))
          : CustomScrollView(
              slivers: <Widget>[
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        const Gap(20),
                        // Article Title
                        Text(
                          mediaItem.title,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color:
                                isDarkMode.value ? Colors.white : Colors.black,
                          ),
                        ),
                        const SizedBox(height: 16),
                        // Publication Date and Views
                        Row(
                          children: <Widget>[
                            Icon(Ionicons.calendar_outline,
                                size: 16, color: metadataStyle.color),
                            const SizedBox(width: 8),
                            Text(
                              DateFormat('d MMMM, yyyy', 'ar')
                                  .format(mediaItem.createdAt),
                              style: metadataStyle,
                            ),
                            const SizedBox(width: 16),
                            Icon(Ionicons.eye_outline,
                                size: 16, color: metadataStyle.color),
                            const SizedBox(width: 8),
                            Text('${mediaItem.visits} مشاهدة',
                                style: metadataStyle),
                          ],
                        ),
                        const SizedBox(height: 10),
                      ],
                    ),
                  ),
                ),
                // Article Image
                if (articleData.imageUrl != null)
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 8.0, bottom: 20),
                      child: UniversalImage(path: articleData.imageUrl!),
                    ),
                  ),
                // Article Content
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Html(
                      data: articleData.text,
                      style: <String, Style>{
                        'body': Style(
                          fontSize: FontSize(fontSize.value),
                          color: isDarkMode.value
                              ? Colors.white70
                              : Colors.black87,
                          direction: ui.TextDirection.rtl,
                          textAlign: TextAlign.justify,
                          lineHeight: const LineHeight(1.8),
                        ),
                        'a': Style(color: Colors.blue.shade600),
                      },
                      onLinkTap: (String? url, _, __) => _launchUrl(url),
                    ),
                  ),
                ),
                const SliverToBoxAdapter(
                  child: Gap(100),
                ),
              ],
            ),
    );
  }

  /// A helper to build the AppBar actions for the article view.
  List<Widget> _buildAppBarActions(ValueNotifier<double> fontSize,
      ValueNotifier<bool> isDarkMode, WidgetRef ref) {
    return <Widget>[
      IconButton(
          icon: const Icon(Icons.text_decrease),
          onPressed: () => fontSize.value = (fontSize.value - 2).clamp(10, 32),
          tooltip: 'تصغير الخط'),
      IconButton(
          icon: const Icon(Icons.text_increase),
          onPressed: () => fontSize.value = (fontSize.value + 2).clamp(10, 32),
          tooltip: 'تكبير الخط'),
      IconButton(
          icon: Icon(isDarkMode.value ? Icons.light_mode : Icons.dark_mode),
          onPressed: () => isDarkMode.value = !isDarkMode.value,
          tooltip: 'تبديل الوضع'),
      IconButton(
          icon: const Icon(Ionicons.share_outline),
          onPressed: () async {
            await ref.read(interactionServiceProvider).shareContent(mediaItem);
          },
          tooltip: 'مشاركة'),
    ];
  }

  /// A helper to safely launch a URL.
  Future<void> _launchUrl(String? url) async {
    if (url != null && await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    }
  }
}
