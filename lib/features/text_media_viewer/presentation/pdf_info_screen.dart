// lib/features/text_media_viewer/presentation/pdf_info_screen.dart
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';
import 'package:open_file/open_file.dart';

import '../../../common/universal_image.dart';
import '../../../common/widgets/bookmark_button.dart';
import '../../../common/widgets/favorite_button.dart';
import '../../../data/models/content_model.dart';
import '../../../data/models/media_player_state.dart'; // Import MediaPlayerState
import '../../../gen/assets.gen.dart';
import '../../../routing/app_router.dart';
import '../../../utils/enum_converters.dart';
import '../../media_player/application/media_player_controller.dart';
import '../../media_player/application/services/service_providers.dart';
import '../../user_data/application/media_providers.dart';

class PdfInfoScreen extends HookConsumerWidget {
  const PdfInfoScreen({
    required this.mediaId,
    super.key,
  });

  final int mediaId;

  /// Get the appropriate text to display based on content type
  String? _getDisplayText(ContentModel content) {
    // For audio files, prefer transcript over description
    if (content.contentType == 'audio') {
      final String? transcript = content.audioData?.transcript;
      if (transcript != null && transcript.isNotEmpty) {
        return transcript;
      }
    }

    // Fallback to description for all content types
    return content.description;
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<ContentModel> asyncMediaItem =
        ref.watch(contentDetailsProvider(mediaId));

    // Listen to the MediaPlayerController's state for download logic
    ref.listen(mediaPlayerControllerProvider(mediaId),
        (AsyncValue<MediaPlayerState>? previous,
            AsyncValue<MediaPlayerState> next) {
      final bool wasDownloading = previous?.value?.isDownloading ?? false;
      final bool isDownloading = next.value?.isDownloading ?? false;
      final String? errorMessage = next.value?.errorMessage;
      final String? downloadedPath = next.value?.downloadedFilePath;

      if (wasDownloading && !isDownloading) {
        if (context.mounted) {
          ScaffoldMessenger.of(context)
              .hideCurrentSnackBar(); // Hide "التحقق من الملف..."
          if (downloadedPath != null && errorMessage == null) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('اكتمل التحميل بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          } else if (errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(errorMessage),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      }
    });

    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: <Widget>[
          // FIX 1: Use .whenData's value directly and handle null case.
          asyncMediaItem.whenData((ContentModel? mediaItem) {
                if (mediaItem == null) {
                  return const SizedBox.shrink();
                }
                return Row(
                  children: <Widget>[
                    FavoriteButton(
                      itemId: mediaItem.id.toString(),
                      itemType: mapContentTypeToEnum(mediaItem.contentType),
                    ),
                    BookmarkButton(
                      itemId: mediaItem.id.toString(),
                      itemType: mapContentTypeToEnum(mediaItem.contentType),
                    ),
                    IconButton(
                      icon: const Icon(Ionicons.share_outline),
                      onPressed: () => ref
                          .read(interactionServiceProvider)
                          .shareContent(mediaItem),
                    ),
                  ],
                );
              }).value ??
              const SizedBox.shrink(),
        ],
      ),
      body: SafeArea(
        child: asyncMediaItem.when(
          data: (ContentModel mediaItem) =>
              _buildPdfInfoDetails(context, mediaItem, ref),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (Object error, StackTrace stack) =>
              Center(child: Text('Error: $error')),
        ),
      ),
    );
  }

  Widget _buildPdfInfoDetails(
      BuildContext context, ContentModel mediaItem, WidgetRef ref) {
    final ThemeData theme = Theme.of(context);
    final BookData? bookData = mediaItem.bookData;

    if (bookData == null) {
      return const Center(child: Text('بيانات الكتاب غير متوفرة'));
    }

    // Watch the MediaPlayerController state to enable/disable the download button
    final AsyncValue<MediaPlayerState> mediaPlayerState =
        ref.watch(mediaPlayerControllerProvider(mediaId));

    final bool canDownload = mediaPlayerState is AsyncData &&
        mediaPlayerState.value?.mediaItem != null &&
        !(mediaPlayerState.value?.isDownloading ?? false);
    final bool isCurrentlyDownloading =
        mediaPlayerState.value?.isDownloading ?? false;

    final String releasedDate =
        DateFormat('dd MMMM HH:mm', 'ar').format(mediaItem.createdAt);

    return Stack(
      children: <Widget>[
        SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(24, 24, 24, 150),
          child: Column(
            children: <Widget>[
              Container(
                decoration: BoxDecoration(
                  boxShadow: <BoxShadow>[
                    BoxShadow(
                      // FIX 2: Use withAlpha instead of withOpacity
                      color: Colors.black.withAlpha(25), // ~10% opacity
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    )
                  ],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: UniversalImage(
                    path: bookData.coverImageUrl ?? Assets.img.drImage.path,
                    height: 280,
                    width: 200,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Text(
                mediaItem.title,
                textAlign: TextAlign.center,
                style: theme.textTheme.headlineSmall
                    ?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: <Widget>[
                  _MetadataItem(
                      label: 'الصفحات',
                      value: bookData.pages?.toString() ?? 'غير محدد'),
                  _MetadataItem(label: 'صدر في', value: releasedDate),
                ],
              ),
              const SizedBox(height: 24),
              if (_getDisplayText(mediaItem) != null &&
                  _getDisplayText(mediaItem)!.isNotEmpty) ...<Widget>[
                Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    'الوصف',
                    textDirection: ui.TextDirection.rtl,
                    style: theme.textTheme.titleMedium
                        ?.copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _getDisplayText(mediaItem)!,
                  style: theme.textTheme.bodyMedium?.copyWith(height: 1.6),
                  textAlign: TextAlign.justify,
                  textDirection: ui.TextDirection.rtl,
                ),
              ],
            ],
          ),
        ),
        Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            // FIX 3: Use withAlpha instead of withOpacity
            color: theme.scaffoldBackgroundColor.withAlpha(242), // ~95% opacity
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                ElevatedButton.icon(
                  icon: Icon(Ionicons.book_outline,
                      color: theme.colorScheme.onPrimary),
                  label: const Text('اقرأ الكتاب'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    minimumSize: const Size(double.infinity, 50),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                  ),
                  onPressed: () => context.pushNamed(
                    SGRoute.textMediaViewer.name,
                    pathParameters: <String, String>{'id': mediaId.toString()},
                  ),
                ),
                const SizedBox(height: 10),
                ElevatedButton.icon(
                  icon: Icon(Ionicons.download_outline,
                      color: theme.colorScheme.onSecondaryContainer),
                  label:
                      isCurrentlyDownloading // Show "جاري التحميل..." if downloading
                          ? Text(
                              'جاري التحميل... ${(mediaPlayerState.value?.downloadProgress ?? 0.0 * 100).toInt()}%')
                          : const Text('تحميل الكتاب'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.secondaryContainer,
                    foregroundColor: theme.colorScheme.onSecondaryContainer,
                    minimumSize: const Size(double.infinity, 50),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                  ),
                  // Disable the button if canDownload is false or already downloading
                  onPressed: canDownload && !isCurrentlyDownloading
                      ? () async {
                          debugPrint('amin ${bookData.pdfUrl}');
                          final ScaffoldMessengerState scaffoldMessenger =
                              ScaffoldMessenger.of(context);
                          scaffoldMessenger.showSnackBar(
                            const SnackBar(content: Text('التحقق من الملف...')),
                          );

                          final ({
                            String? error,
                            File? fileObject,
                            bool isSuccess,
                            String? path
                          }) result = await ref
                              .read(mediaPlayerControllerProvider(mediaId)
                                  .notifier)
                              .downloadMedia();

                          if (!context.mounted) {
                            return;
                          }
                          // Snackbars are now handled by the ref.listen in the build method.
                          // Remove the direct snackbar handling here.
                          // scaffoldMessenger.hideCurrentSnackBar(); // This might hide the "success" snackbar too
                          if (result.isSuccess &&
                              result.path != null &&
                              result.error != null) {
                            // File already downloaded scenario, show it
                            await OpenFile.open(result.path);
                          }
                        }
                      : null, // Disable onPressed if not ready to download
                ),
                // Show a linear progress indicator if currently downloading
                if (isCurrentlyDownloading)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: LinearProgressIndicator(
                      value: mediaPlayerState.value?.downloadProgress,
                      backgroundColor: Colors.grey.shade300,
                      valueColor: AlwaysStoppedAnimation<Color>(
                          theme.colorScheme.primary),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _MetadataItem extends StatelessWidget {
  const _MetadataItem({required this.label, required this.value});
  final String label;
  final String value;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Text(label, style: Theme.of(context).textTheme.labelMedium),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context)
              .textTheme
              .titleSmall
              ?.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }
}
