import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../data/models/content_model.dart';
import '../../user_data/application/media_providers.dart';
import 'article_reader_screen.dart';
import 'pdf_reader_screen.dart';

class TextMediaRouterScreen extends ConsumerWidget {
  const TextMediaRouterScreen({
    required this.mediaId,
    super.key,
  });

  final int mediaId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Fetch the content details
    final AsyncValue<ContentModel> mediaItemAsync =
        ref.watch(contentDetailsProvider(mediaId));

    return mediaItemAsync.when(
      data: (ContentModel mediaItem) {
        if (mediaItem == null) {
          return const Scaffold(body: Center(child: Text('المحتوى غير موجود')));
        }

        // Route to the correct viewer based on content type
        switch (mediaItem.contentType) {
          case 'book':
            if (mediaItem.bookData == null) {
              return const Scaffold(
                  body: Center(child: Text('بيانات الكتاب غير موجودة')));
            }
            return PdfReaderScreen(mediaItem: mediaItem);
          case 'article':
            if (mediaItem.articleData == null) {
              return const Scaffold(
                  body: Center(child: Text('بيانات المقال غير موجودة')));
            }
            return ArticleReaderScreen(mediaItem: mediaItem);
          default:
            return Scaffold(
                body: Center(
                    child:
                        Text('صيغة نص غير مدعومة: ${mediaItem.contentType}')));
        }
      },
      loading: () =>
          const Scaffold(body: Center(child: CircularProgressIndicator())),
      error: (Object err, StackTrace stack) =>
          Scaffold(body: Center(child: Text('خطأ: $err'))),
    );
  }
}
