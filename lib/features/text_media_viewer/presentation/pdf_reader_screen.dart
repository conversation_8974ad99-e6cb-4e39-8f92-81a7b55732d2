import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

import '../../../common/widgets/category_app_bar.dart';
import '../../../data/models/content_model.dart';

class PdfReaderScreen extends HookConsumerWidget {
  const PdfReaderScreen({
    required this.mediaItem,
    super.key,
  });

  final ContentModel mediaItem;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final PdfViewerController pdfViewerController =
        useMemoized(() => PdfViewerController());
    final String? pdfUrl = mediaItem.bookData?.pdfUrl;

    return Scaffold(
      appBar: CategoryAppBar(
        contentId: mediaItem.id,
        title: mediaItem.title,
        actions: <Widget>[
          if (pdfUrl != null)
            IconButton(
              icon: const Icon(Icons.zoom_in),
              onPressed: () => pdfViewerController.zoomLevel =
                  pdfViewerController.zoomLevel + 0.5,
            ),
          if (pdfUrl != null)
            IconButton(
              icon: const Icon(Icons.zoom_out),
              onPressed: () => pdfViewerController.zoomLevel =
                  pdfViewerController.zoomLevel - 0.5,
            ),
        ],
      ),
      body: pdfUrl != null
          ? SfPdfViewer.network(
              pdfUrl,
              controller: pdfViewerController,
            )
          : const Center(
              child: Text('No PDF URL available for this book.'),
            ),
    );
  }
}
