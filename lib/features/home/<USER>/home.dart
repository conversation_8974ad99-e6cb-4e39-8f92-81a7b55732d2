// lib/features/home/<USER>/home.dart

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../common/universal_image.dart';
import '../../../common/widgets/media_list_item.dart';
import '../../../constants/colors.dart';
import '../../../data/models/api_response.dart';
import '../../../data/models/category_model.dart';
import '../../../data/models/content_model.dart';
import '../../../gen/assets.gen.dart';
import '../../../routing/app_router.dart';
import '../../../utils/confirmation_dialog.dart';
import '../../../utils/exit_confirmation.dart';
import '../../../utils/report_error_dialog.dart';
import '../../reels/presentation/widgets/reels_grid_view.dart';
import '../../settings/presentation/settings_tile.dart';
import '../../user_data/application/media_providers.dart';
import '../../user_data/application/user_data_providers.dart';

part 'home.g.dart';

@riverpod
class SelectedMainTab extends _$SelectedMainTab {
  @override
  int build() => 0;
  set index(int value) => state = value; // Converted to setter
}

@riverpod
class SelectedSecondaryTab extends _$SelectedSecondaryTab {
  @override
  int build() => 0;
  set index(int value) => state = value; // Converted to setter
}

class HomeScreen extends HookConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<List<CategoryModel>> categoriesAsync =
        ref.watch(categoriesProvider);

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (didPop) {
          return;
        }
        final bool shouldPop = await showExitConfirmation(context);
        if (shouldPop && context.mounted) {
          if (context.canPop()) {
            context.pop();
          } else {
            SystemNavigator.pop();
          }
        }
      },
      child: Scaffold(
        drawer: const _AppDrawer(),
        appBar: const CustomAppbar(),
        body: SafeArea(
          child: categoriesAsync.when(
            data: (List<CategoryModel> categories) {
              if (categories.isEmpty) {
                return const Center(child: Text('لا توجد فئات لعرضها'));
              }
              return _HomeScreenContent(categories: categories);
            },
            loading: () => const Padding(
              padding: EdgeInsets.only(top: 20),
              child: Center(child: CircularProgressIndicator()),
            ),
            error: (Object err, StackTrace stack) =>
                Center(child: Text('خطأ: $err')),
          ),
        ),
      ),
    );
  }
}

class _HomeScreenContent extends ConsumerStatefulWidget {
  const _HomeScreenContent({required this.categories});
  final List<CategoryModel> categories;

  @override
  ConsumerState<_HomeScreenContent> createState() => _HomeScreenContentState();
}

class _HomeScreenContentState extends ConsumerState<_HomeScreenContent>
    with SingleTickerProviderStateMixin {
  late TabController _mainTabController;

  @override
  void initState() {
    super.initState();
    _mainTabController =
        TabController(length: widget.categories.length, vsync: this);
    _mainTabController.addListener(_handleTabChange);
  }

  void _handleTabChange() {
    if (ref.read(selectedMainTabProvider) != _mainTabController.index) {
      ref.read(selectedMainTabProvider.notifier).index =
          _mainTabController.index; // Using setter
      ref.read(selectedSecondaryTabProvider.notifier).index = 0; // Using setter
    }
  }

  @override
  void dispose() {
    _mainTabController.removeListener(_handleTabChange);
    _mainTabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // FIX: Wrap the CustomScrollView with a RefreshIndicator
    return RefreshIndicator(
      onRefresh: () async {
        // Invalidate the providers to force a refetch of all content
        ref.invalidate(categoriesProvider);
        ref.invalidate(categoryContentsProvider);
        // We await the categoriesProvider to ensure the indicator stays
        // visible until the main categories have been re-fetched.
        await ref.read(categoriesProvider.future);
      },
      child: CustomScrollView(
        slivers: <Widget>[
          const SliverToBoxAdapter(child: ShaikArabicFont()),
          _MainCategoriesTabBar(
            categories: widget.categories,
            tabController: _mainTabController,
          ),
          _SecondaryCategoriesTabBar(categories: widget.categories),
          _ContentArea(categories: widget.categories),
          const SliverToBoxAdapter(child: SizedBox(height: 100)),
        ],
      ),
    );
  }
}

class CustomAppbar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppbar({super.key});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(10),
          bottomRight: Radius.circular(10),
        ),
      ),
      leading: Builder(
        builder: (BuildContext context) => IconButton(
          icon: const Icon(Icons.menu),
          onPressed: () => Scaffold.of(context).openDrawer(),
          tooltip: 'القائمة الجانبية',
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class ShaikArabicFont extends StatelessWidget {
  const ShaikArabicFont({super.key, this.showSearch = true});
  final bool showSearch;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: MediaQuery.sizeOf(context).width * 0.1,
      ),
      child: Column(
        children: <Widget>[
          const Gap(10),
          UniversalImage(
            path: Assets.img.group58.path,
            fit: BoxFit.contain,
            height: 151,
            width: 338,
          ),
          if (showSearch) ...<Widget>[
            const Gap(10),
            GestureDetector(
              onTap: () => context.push(SGRoute.search.route),
              child: AbsorbPointer(
                child: CupertinoSearchTextField(
                  placeholder: 'ابحث عن الدروس، الخطب، المقالات...',
                  placeholderStyle: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(height: 1, color: klightBlackColor),
                  decoration: BoxDecoration(
                    color: kPriColor,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(color: kGrayColor),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class _MainCategoriesTabBar extends StatelessWidget {
  const _MainCategoriesTabBar({
    required this.categories,
    required this.tabController,
  });
  final List<CategoryModel> categories;
  final TabController tabController;

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: TabBar(
        controller: tabController,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        splashBorderRadius: BorderRadius.circular(6),
        indicatorSize: TabBarIndicatorSize.label,
        unselectedLabelColor: klightBlackColor,
        labelColor: kBlackColor,
        labelStyle: Theme.of(context)
            .textTheme
            .titleSmall
            ?.copyWith(fontWeight: FontWeight.bold),
        tabs: categories
            .map((CategoryModel category) => Tab(text: category.name))
            .toList(),
      ),
    );
  }
}

class _SecondaryCategoriesTabBar extends ConsumerWidget {
  const _SecondaryCategoriesTabBar({required this.categories});
  final List<CategoryModel> categories;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final int selectedMainTabIndex = ref.watch(selectedMainTabProvider);
    if (selectedMainTabIndex >= categories.length) {
      return const SliverToBoxAdapter();
    }

    final CategoryModel selectedMainCategory = categories[selectedMainTabIndex];
    final List<CategoryModel>? secondaryTabs =
        selectedMainCategory.subcategories;

    if (secondaryTabs == null || secondaryTabs.isEmpty) {
      return const SliverToBoxAdapter();
    }

    final int selectedSecondaryTab = ref.watch(selectedSecondaryTabProvider);

    return SliverToBoxAdapter(
      child: Container(
        height: 60,
        color: kPriLightColor,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: secondaryTabs.length,
          itemBuilder: (BuildContext context, int index) {
            final bool isSelected = index == selectedSecondaryTab;
            final CategoryModel tab = secondaryTabs[index];
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 12),
              child: InkWell(
                onTap: () => ref
                    .read(selectedSecondaryTabProvider.notifier)
                    .index = index, // Using setter
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: isSelected ? kSelectedColor : kPriColor,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: isSelected
                          ? Theme.of(context)
                              .colorScheme
                              .primary
                              .withValues(alpha: 0.5) // Fixed .withAlpha
                          : kGrayColor,
                    ),
                  ),
                  child: Text(
                    tab.name,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          color: isSelected
                              ? Theme.of(context).colorScheme.primary
                              : klightBlackColor,
                          fontSize: 14,
                        ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class _ContentArea extends ConsumerWidget {
  const _ContentArea({required this.categories});
  final List<CategoryModel> categories;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final int selectedMainTabIndex = ref.watch(selectedMainTabProvider);
    final int selectedSecondaryTabIndex =
        ref.watch(selectedSecondaryTabProvider);

    if (selectedMainTabIndex >= categories.length) {
      return const SliverToBoxAdapter();
    }

    final CategoryModel selectedMainCategory = categories[selectedMainTabIndex];

    if (selectedMainCategory.name == 'مقاطع قصيرة') {
      return const SliverToBoxAdapter(child: ReelsGridView());
    }

    final bool hasSubcategories =
        selectedMainCategory.subcategories?.isNotEmpty ?? false;
    final int categoryId = hasSubcategories
        ? selectedMainCategory.subcategories![selectedSecondaryTabIndex].id
        : selectedMainCategory.id;

    return _CategoryContentList(categoryId: categoryId);
  }
}

class _CategoryContentList extends ConsumerWidget {
  const _CategoryContentList({required this.categoryId});
  final int categoryId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<PaginatedContentResponse> contentsAsync =
        ref.watch(categoryContentsProvider(categoryId));

    return contentsAsync.when(
      data: (PaginatedContentResponse paginatedResponse) {
        final List<ContentModel> items = paginatedResponse.data.contents;
        if (items.isEmpty) {
          return const SliverToBoxAdapter(
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(20.0),
                child: Text('لا يوجد محتوى في هذا التصنيف.'),
              ),
            ),
          );
        }
        return SliverList.builder(
          itemCount: items.length,
          itemBuilder: (BuildContext context, int index) {
            final ContentModel item = items[index];
            return MediaListItem(
              item: item,
              // onTap is handled by MediaListItem's internal _defaultNavigate,
              // or you can pass a specific onTap here if needed.
              // onTap: () => _navigateToContent(context, item), // If _navigateToContent is a local helper
            );
          },
        );
      },
      loading: () => const SliverToBoxAdapter(
        child: Padding(
          padding: EdgeInsets.only(top: 20),
          child: Center(child: CircularProgressIndicator()),
        ),
      ),
      error: (Object err, StackTrace stack) =>
          SliverToBoxAdapter(child: Center(child: Text('خطأ: $err'))),
    );
  }
}

class _AppDrawer extends ConsumerWidget {
  const _AppDrawer();

  Future<void> _confirmClearData(
    BuildContext context,
    WidgetRef ref,
    String dataType,
    Future<void> Function() clearAction,
  ) async {
    final bool? confirm = await ConfirmationDialog.showClearData(
        context: context, dataType: dataType);
    if ((confirm ?? true) && context.mounted) {
      await clearAction();
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تم حذف $dataType بنجاح')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: <Widget>[
          DrawerHeader(
            decoration: BoxDecoration(color: Theme.of(context).primaryColor),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                CircleAvatar(
                    radius: 30,
                    backgroundImage: AssetImage(Assets.img.launcherIcon.path)),
              ],
            ),
          ),
          SettingsTile(
            title: 'نبذة عن الشيخ',
            icon: Icons.person_outline,
            onTap: () {
              Navigator.pop(context);
              context.push(SGRoute.sheikhInfo.route);
            },
          ),
          SettingsTile(
            title: 'المفضلة',
            icon: Ionicons.heart,
            onTap: () => context.push(SGRoute.favorites.route),
            trailing: IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _confirmClearData(
                context,
                ref,
                'المفضلة',
                () => ref.read(favoritesProvider.notifier).clearFavorites(),
              ),
            ),
          ),
          SettingsTile(
            title: 'المحفوظات',
            icon: Ionicons.bookmark,
            onTap: () => context.push(SGRoute.bookmarks.route),
            trailing: IconButton(
              icon: const Icon(Icons.delete),
              onPressed: () => _confirmClearData(
                context,
                ref,
                'المحفوظات',
                () => ref.read(bookmarksProvider.notifier).clearBookmarks(),
              ),
            ),
          ),
          ListTile(
            leading: const Icon(Icons.info),
            title: const Text('حول التطبيق'),
            onTap: () {
              Navigator.pop(context);
              context.push(SGRoute.aboutUs.route);
            },
          ),
          ListTile(
            leading: const Icon(Icons.contact_support),
            title: const Text('ابلاغ عن خطأ'),
            onTap: () {
              Navigator.pop(context);
              showReportErrorDialog(context);
            },
          ),
        ],
      ),
    );
  }
}
