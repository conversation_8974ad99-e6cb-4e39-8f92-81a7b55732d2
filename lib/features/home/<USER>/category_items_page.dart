// lib/features/home/<USER>/category_items_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../common/widgets/paginated_content_list.dart';
import '../../../common/widgets/pagination_info_bar.dart';
import '../../../constants/colors.dart';
import '../../../data/models/content_model.dart';
import '../../../routing/app_router.dart';

// NEW: A dedicated, type-safe class for navigation arguments.
class CategoryItemsPageArgs {
  const CategoryItemsPageArgs({
    required this.categoryId,
    required this.categoryName,
  });

  final int categoryId;
  final String categoryName;
}

class CategoryItemsPage extends ConsumerWidget {
  const CategoryItemsPage({
    super.key,
    required this.categoryId,
    required this.categoryName,
  });

  final int categoryId;
  final String categoryName;

  void _navigateToContentDetails(BuildContext context, ContentModel item) {
    final String contentId = item.id.toString();

    switch (item.contentType) {
      case 'book':
        context.pushNamed(
          SGRoute.pdfInfo.name,
          pathParameters: <String, String>{'id': contentId},
        );
        break;
      case 'article':
        context.pushNamed(
          SGRoute.textMediaViewer.name,
          pathParameters: <String, String>{'id': contentId},
        );
        break;
      case 'audio':
      case 'video':
        context.pushNamed(
          SGRoute.mediaPlayer.name,
          pathParameters: <String, String>{'id': contentId},
        );
        break;
      case 'post':
        context.pushNamed(
          SGRoute.tweetDetails.name,
          pathParameters: <String, String>{'id': contentId},
        );
        break;
      default:
        context.pushNamed(
          SGRoute.mediaPlayer.name,
          pathParameters: <String, String>{'id': contentId},
        );
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: <Widget>[
            Expanded(child: Text(categoryName)),
            CompactPaginationInfo(categoryId: categoryId),
          ],
        ),
        backgroundColor: kPriColor,
      ),
      body: Column(
        children: <Widget>[
          Expanded(
            child: PaginatedContentList(
              categoryId: categoryId,
              onItemTap: (ContentModel item) =>
                  _navigateToContentDetails(context, item),
              showLoadMoreButton: false,
            ),
          ),
          PaginationInfoBar(
            categoryId: categoryId,
            showLoadMoreButton: true,
          ),
        ],
      ),
      floatingActionButton: PaginationFAB(categoryId: categoryId),
    );
  }
}
