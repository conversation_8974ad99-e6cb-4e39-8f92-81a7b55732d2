// lib/features/home/<USER>/category_items_page.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../common/widgets/media_list_item.dart';
import '../../../constants/colors.dart';
import '../../../data/models/api_response.dart';
import '../../../data/models/content_model.dart';
import '../../../routing/app_router.dart';
import '../../user_data/application/media_providers.dart';

// NEW: A dedicated, type-safe class for navigation arguments.
class CategoryItemsPageArgs {
  const CategoryItemsPageArgs({
    required this.categoryId,
    required this.categoryName,
  });

  final int categoryId;
  final String categoryName;
}

class CategoryItemsPage extends ConsumerWidget {
  const CategoryItemsPage({
    super.key,
    required this.categoryId,
    required this.categoryName,
  });

  final int categoryId;
  final String categoryName;

  void _navigateToContentDetails(BuildContext context, ContentModel item) {
    final String contentId = item.id.toString();

    switch (item.contentType) {
      case 'book':
        context.pushNamed(
          SGRoute.pdfInfo.name,
          pathParameters: <String, String>{'id': contentId},
        );
        break;
      case 'article':
        context.pushNamed(
          SGRoute.textMediaViewer.name,
          pathParameters: <String, String>{'id': contentId},
        );
        break;
      case 'audio':
      case 'video':
        context.pushNamed(
          SGRoute.mediaPlayer.name,
          pathParameters: <String, String>{'id': contentId},
        );
        break;
      case 'post':
        context.pushNamed(
          SGRoute.tweetDetails.name,
          pathParameters: <String, String>{'id': contentId},
        );
        break;
      default:
        context.pushNamed(
          SGRoute.mediaPlayer.name,
          pathParameters: <String, String>{'id': contentId},
        );
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<PaginatedContentResponse> contentsAsync =
        ref.watch(categoryContentsProvider(categoryId));

    return Scaffold(
      appBar: AppBar(
        title: Text(categoryName),
        backgroundColor: kPriColor,
      ),
      body: contentsAsync.when(
        data: (PaginatedContentResponse paginatedResponse) {
          // FIX: Access the list via `paginatedResponse.data.contents`
          final List<ContentModel> items = paginatedResponse.data.contents;
          if (items.isEmpty) {
            return const Center(
              child: Text('لا توجد عناصر في هذه الفئة.'),
            );
          }

          return ListView.builder(
            itemCount: items.length,
            itemBuilder: (BuildContext context, int index) {
              final ContentModel item = items[index];
              return MediaListItem(
                // Changed from MediaListItem.fromContentModel
                item: item, // Pass the item directly
                onTap: () => _navigateToContentDetails(context, item),
              );
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (Object err, StackTrace stack) =>
            Center(child: Text('Error: $err')),
      ),
    );
  }
}
