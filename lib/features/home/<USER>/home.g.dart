// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(SelectedMainTab)
const selectedMainTabProvider = SelectedMainTabProvider._();

final class SelectedMainTabProvider
    extends $NotifierProvider<SelectedMainTab, int> {
  const SelectedMainTabProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'selectedMainTabProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$selectedMainTabHash();

  @$internal
  @override
  SelectedMainTab create() => SelectedMainTab();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$selectedMainTabHash() => r'd2e4b1593b53a52d776fe92e7a41ddee450e83c7';

abstract class _$SelectedMainTab extends $Notifier<int> {
  int build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<int, int>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<int, int>, int, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(SelectedSecondaryTab)
const selectedSecondaryTabProvider = SelectedSecondaryTabProvider._();

final class SelectedSecondaryTabProvider
    extends $NotifierProvider<SelectedSecondaryTab, int> {
  const SelectedSecondaryTabProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'selectedSecondaryTabProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$selectedSecondaryTabHash();

  @$internal
  @override
  SelectedSecondaryTab create() => SelectedSecondaryTab();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }
}

String _$selectedSecondaryTabHash() =>
    r'cfd12e933bec282edd3edfec79770c0e5e052500';

abstract class _$SelectedSecondaryTab extends $Notifier<int> {
  int build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<int, int>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<int, int>, int, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
