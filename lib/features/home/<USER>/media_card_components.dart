// lib/features/home/<USER>/media_card_components.dart
import 'dart:ui' as ui;

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:ionicons/ionicons.dart';

import '../../../common/svg_icon.dart';
import '../../../common/universal_image.dart';
import '../../../constants/colors.dart';
import '../../../data/models/content_model.dart';
import '../../../gen/assets.gen.dart';
import '../../../utils/format_utils.dart';

class VideoCardContent extends StatelessWidget {
  const VideoCardContent({super.key, required this.item});
  final ContentModel item;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const SizedBox(height: 8),
        AspectRatio(
          aspectRatio: 16 / 9,
          child: Stack(
            children: <Widget>[
              MediaThumbnail(
                thumbnailUrl: item.videoData?.thumbnailUrl,
                fallbackIcon: Icons.play_arrow,
              ),
              Center(
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(50)),
                  child: const Icon(Icons.play_arrow,
                      color: Colors.white, size: 32),
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              MediaTitle(title: item.title),
              if (item.description != null &&
                  item.description!.isNotEmpty) ...<Widget>[
                const SizedBox(height: 4),
                MediaDescription(description: item.description),
              ],
              const SizedBox(height: 8),
              MediaInfoRow(item: item),
            ],
          ),
        ),
      ],
    );
  }
}

class AudioCardContent extends StatelessWidget {
  const AudioCardContent({super.key, required this.item});
  final ContentModel item;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: Row(
        children: <Widget>[
          SvgIcon(
              image: Assets.svg.headphone,
              height: 40,
              width: 40,
              color: kPrimaryLight),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                MediaTitle(title: item.title, maxLines: 2),
                const SizedBox(height: 4),
                MediaDescription(description: item.description ?? ''),
                const SizedBox(height: 4),
                MediaInfoRow(item: item),
              ],
            ),
          ),
          const Gap(30),
          SvgIcon(
              image: Assets.svg.arrowNext,
              height: 12,
              width: 6,
              color: kPrimaryLight),
        ],
      ),
    );
  }
}

class BookCardContent extends StatelessWidget {
  const BookCardContent(
      {super.key, required this.item, required this.hideImage});
  final ContentModel item;
  final bool hideImage;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          if (!hideImage) DocumentIconContainer(item: item),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                MediaTitle(title: item.title),
                const SizedBox(height: 8),
                MediaInfoRow(item: item, showPages: true),
                if (item.description != null &&
                    item.description!.isNotEmpty) ...<Widget>[
                  const SizedBox(height: 8),
                  if (!hideImage)
                    Text(
                      'نبذة عن الكتاب:',
                      style: Theme.of(context)
                          .textTheme
                          .titleSmall
                          ?.copyWith(fontSize: 10, fontWeight: FontWeight.bold),
                    ),
                  const SizedBox(height: 4),
                  MediaDescription(description: item.description),
                ],
              ],
            ),
          ),
          const Gap(30),
          Padding(
            padding: const EdgeInsets.only(top: 60),
            child: SvgIcon(
                image: Assets.svg.arrowNext,
                height: 12,
                width: 6,
                color: kPrimaryLight),
          ),
        ],
      ),
    );
  }
}

class ArticleCardContent extends StatelessWidget {
  const ArticleCardContent({super.key, required this.item});
  final ContentModel item;

  @override
  Widget build(BuildContext context) {
    // Use the item's description as the snippet.
    final String? snippet = item.description;
    final String? imageUrl = item.articleData?.imageUrl;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // Text content on the left, takes up remaining space
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  item.contentTypeLabel, // Shows "مقال"
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 6),
                MediaTitle(title: item.title, maxLines: 2),
                const SizedBox(height: 6),
                MediaDescription(description: snippet, maxLines: 3),
                const SizedBox(height: 8),
                MediaInfoRow(item: item),
              ],
            ),
          ),
          // Image on the right, if it exists
          if (imageUrl != null) ...<Widget>[
            const SizedBox(width: 16),
            ClipRRect(
              borderRadius: BorderRadius.circular(8.0),
              child: UniversalImage(
                path: imageUrl,
                width: 100,
                height: 100,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class DefaultCardContent extends StatelessWidget {
  const DefaultCardContent({super.key, required this.item});
  final ContentModel item;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Row(
        children: <Widget>[
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                MediaTitle(title: item.title),
                if (item.description != null) ...<Widget>[
                  const SizedBox(height: 4),
                  MediaDescription(description: item.description),
                ],
              ],
            ),
          ),
          const SizedBox(width: 16),
          SvgIcon(
              image: Assets.svg.arrowNext,
              height: 12,
              width: 6,
              color: kPrimaryLight),
        ],
      ),
    );
  }
}

// --- Reusable Helper Widgets (Restored and Adapted) ---

class MediaTitle extends StatelessWidget {
  const MediaTitle({super.key, required this.title, this.maxLines = 1});
  final String title;
  final int maxLines;
  @override
  Widget build(BuildContext context) => Text(
        title,
        maxLines: maxLines,
        overflow: TextOverflow.ellipsis,
        style: Theme.of(context)
            .textTheme
            .titleSmall
            ?.copyWith(fontWeight: FontWeight.bold, fontSize: 14),
      );
}

class MediaDescription extends StatelessWidget {
  const MediaDescription(
      {super.key, this.description, this.maxLines = 2, this.overflow});
  final String? description;
  final int maxLines;
  final TextOverflow? overflow;
  @override
  Widget build(BuildContext context) {
    return description != null
        ? Text(
            description!,
            textAlign: TextAlign.justify,
            textDirection: ui.TextDirection.rtl,
            style:
                Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 10),
            maxLines: maxLines,
            overflow: overflow,
          )
        : const SizedBox();
  }
}

class MediaThumbnail extends StatelessWidget {
  const MediaThumbnail({super.key, this.thumbnailUrl, this.fallbackIcon});
  final String? thumbnailUrl;
  final IconData? fallbackIcon;
  @override
  Widget build(BuildContext context) {
    final String? nonnullThumb = thumbnailUrl != null &&
            thumbnailUrl ==
                'https://img.youtube.com/vi/Hh60s9geb4w/maxresdefault.jpg'
        ? Assets.img.drImage.path
        : thumbnailUrl;
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      clipBehavior: Clip.antiAlias,
      child: UniversalImage(
          path: nonnullThumb!,
          // fallbackIcon: fallbackIcon,
          width: double.infinity,
          height: double.infinity),
    );
  }
}

class OptionsIcon extends StatelessWidget {
  const OptionsIcon({super.key, required this.image, this.title, this.date});
  final DateTime? date;
  final String? title;
  final String image;
  @override
  Widget build(BuildContext context) {
    String displayText = title ?? '';
    if (date != null) {
      displayText = formatDate(date!);
    }
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        SvgIcon(image: image, height: 14, width: 14, color: kPrimaryLight),
        const SizedBox(width: 4),
        Text(
          displayText,
          style: Theme.of(context)
              .textTheme
              .titleSmall
              ?.copyWith(fontSize: 12, color: klightBlackColor),
        ),
      ],
    );
  }
}

class BookInfo extends StatelessWidget {
  const BookInfo(
      {super.key, required this.optionTitle, required this.optionValue});
  final String optionTitle;
  final String optionValue;
  @override
  Widget build(BuildContext context) {
    final TextStyle? textStyle =
        Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 10);
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Text(optionTitle, style: textStyle),
        const SizedBox(width: 4),
        Text(optionValue, style: textStyle),
      ],
    );
  }
}

class MediaInfoRow extends StatelessWidget {
  const MediaInfoRow({super.key, required this.item, this.showPages = false});
  final ContentModel item;
  final bool showPages;

  @override
  Widget build(BuildContext context) {
    final String? duration =
        item.audioData?.duration ?? item.videoData?.duration;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          children: <Widget>[
            if (!showPages &&
                duration != null &&
                duration.isNotEmpty) ...<Widget>[
              OptionsIcon(image: Assets.svg.replay, title: duration),
              const SizedBox(width: 10),
            ],
            if (!showPages) ...<Widget>[
              OptionsIcon(image: Assets.svg.calender, date: item.createdAt),
              _visited(context),
            ],
          ],
        ),
        if (showPages) ...<Widget>[
          _buildBookInfo(context, item.bookData),
        ],
      ],
    );
  }

  Padding _visited(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Row(
        children: <Widget>[
          const Icon(
            Ionicons.eye_outline,
            size: 16,
            color: kPrimaryLight,
          ),
          const SizedBox(width: 4),
          Text(
            '${item.visits}',
            style: Theme.of(context)
                .textTheme
                .titleSmall
                ?.copyWith(fontSize: 12, color: klightBlackColor),
          ),
        ],
      ),
    );
  }

  Widget _buildBookInfo(BuildContext context, BookData? bookData) {
    // Return empty if no book data
    if (bookData == null) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(top: 6),
      child: Column(
        mainAxisSize: MainAxisSize.min, // Added to prevent unbounded height
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          if (bookData.pages != null) ...<Widget>[
            BookInfo(
                optionTitle: 'عدد الصفحات:', optionValue: '${bookData.pages}'),
          ],
          if (bookData.publishedDate !=
              null) // This data comes from the new API
            BookInfo(
                optionTitle: 'ت/ الإضافة',
                optionValue: bookData.publishedDate!),
          if (bookData.publisher != null) // This data comes from the new API
            BookInfo(optionTitle: 'الناشر:', optionValue: bookData.publisher!)
        ],
      ),
    );
  }
}

class DocumentIconContainer extends StatelessWidget {
  const DocumentIconContainer(
      {super.key, required this.item, this.isArticle = false});
  final ContentModel item;
  final bool isArticle;
  @override
  Widget build(BuildContext context) {
    final String? thumbnailUrl =
        isArticle ? item.articleData?.imageUrl : item.bookData?.coverImageUrl;
    return Container(
      width: 67,
      height: 100,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(4),
        boxShadow: const <BoxShadow>[
          BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(0, 2))
        ],
      ),
      child: UniversalImage(
        path: thumbnailUrl!,
        // fallbackIcon: isArticle ? Icons.article_outlined : Icons.menu_book,
      ),
    );
  }
}

class ExpandableMediaDescription extends ConsumerWidget {
  const ExpandableMediaDescription({
    super.key,
    required this.description,
    this.style,
    this.maxLines = 1,
    this.padding,
    this.readMoreText = '... المزيد',
    this.readMoreStyle,
    this.onReadMorePressed,
  });

  final String description;
  final TextStyle? style;
  final int maxLines;
  final EdgeInsetsGeometry? padding;
  final String readMoreText;
  final TextStyle? readMoreStyle;
  final VoidCallback? onReadMorePressed;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final TextStyle baseStyle = style ??
        Theme.of(context).textTheme.bodySmall?.copyWith(fontSize: 12) ??
        const TextStyle(fontSize: 12, color: Colors.black);

    final TextStyle moreStyle = readMoreStyle ??
        baseStyle.copyWith(
          color: Theme.of(context).primaryColor,
          fontWeight: FontWeight.bold,
        );

    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          final TextPainter textPainter = TextPainter(
            text: TextSpan(text: description, style: baseStyle),
            textDirection: ui.TextDirection.rtl,
            maxLines: maxLines,
            ellipsis: '…',
          )..layout(maxWidth: constraints.maxWidth);

          if (!textPainter.didExceedMaxLines) {
            return Text(
              description,
              style: baseStyle,
              textAlign: TextAlign.justify,
              textDirection: ui.TextDirection.rtl,
            );
          }

          // Estimate the trim length based on how much space the "read more" button needs
          final int readMoreBuffer = readMoreText.length;

          // Find last visible index
          final double lineHeight = textPainter.preferredLineHeight;
          final double lastLineY = maxLines * lineHeight;

          final TextPosition endPosition = textPainter.getPositionForOffset(
            Offset(constraints.maxWidth, lastLineY),
          );

          final int endIndex =
              textPainter.getOffsetBefore(endPosition.offset) ?? 0;

          // Trim slightly earlier to fit "... المزيد"
          final int trimmedIndex =
              (endIndex - readMoreBuffer).clamp(0, description.length);
          String trimmed = description.substring(0, trimmedIndex).trimRight();

          // Ensure we don't break in the middle of a word
          if (trimmed.contains(' ')) {
            trimmed = trimmed.substring(0, trimmed.lastIndexOf(' '));
          }

          return RichText(
            textDirection: ui.TextDirection.rtl,
            textAlign: TextAlign.justify,
            text: TextSpan(
              style: baseStyle,
              children: <InlineSpan>[
                TextSpan(text: '$trimmed '),
                TextSpan(
                  text: readMoreText,
                  style: moreStyle,
                  recognizer: TapGestureRecognizer()..onTap = onReadMorePressed,
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
