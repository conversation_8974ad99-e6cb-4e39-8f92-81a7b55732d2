import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../common/widgets/user_data_list_item.dart';
import '../../../data/models/user_data_models.dart';
import '../../user_data/application/user_data_providers.dart';

class FavoritesScreen extends ConsumerWidget {
  const FavoritesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Use the new provider name 'favoritesProvider'
    final AsyncValue<List<FavoriteItem>> favoritesAsync =
        ref.watch(favoritesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('المفضلة'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.delete_forever),
            onPressed: () =>
                ref.read(favoritesProvider.notifier).clearFavorites(),
            tooltip: 'حذف كل المفضلة',
          )
        ],
      ),
      body: favoritesAsync.when(
        data: (List<FavoriteItem> items) {
          if (items.isEmpty) {
            return const Center(child: Text('لا يوجد عناصر في المفضلة'));
          }
          return ListView.builder(
            itemCount: items.length,
            itemBuilder: (BuildContext context, int index) {
              final FavoriteItem item = items[index];
              return UserDataListItem.favorite(
                itemId: item.itemId,
                dateTime: item.createdAt,
                onRemove: () {
                  // The `toggleFavorite` method now has all the info it needs
                  ref.read(favoritesProvider.notifier).toggleFavorite(
                        itemId: item.itemId,
                        type: item.type,
                      );
                },
              );
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (Object err, StackTrace stack) =>
            Center(child: Text('خطأ: $err')),
      ),
    );
  }
}
