import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../common/widgets/user_data_list_item.dart';
import '../../../data/models/user_data_models.dart';
import '../../user_data/application/user_data_providers.dart';

class BookmarksScreen extends ConsumerWidget {
  const BookmarksScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<List<BookmarkItem>> bookmarksAsync =
        ref.watch(bookmarksProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('المحفوظات'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.delete_forever),
            onPressed: () =>
                ref.read(bookmarksProvider.notifier).clearBookmarks(),
          )
        ],
      ),
      body: bookmarksAsync.when(
        data: (List<BookmarkItem> items) {
          if (items.isEmpty) {
            return const Center(child: Text('لا يوجد عناصر في المحفوظات'));
          }
          return ListView.builder(
            itemCount: items.length,
            itemBuilder: (BuildContext context, int index) {
              final BookmarkItem item = items[index];
              return UserDataListItem.bookmark(
                itemId: item.itemId,
                dateTime: item.createdAt,
                onRemove: () {
                  ref.read(bookmarksProvider.notifier).toggleBookmark(
                        itemId: item.itemId,
                        type: item.type,
                      );
                },
              );
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (Object err, StackTrace stack) =>
            Center(child: Text('Error: $err')),
      ),
    );
  }
}
