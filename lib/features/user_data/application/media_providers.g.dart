// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'media_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(categories)
const categoriesProvider = CategoriesProvider._();

final class CategoriesProvider extends $FunctionalProvider<
        AsyncValue<List<CategoryModel>>,
        List<CategoryModel>,
        FutureOr<List<CategoryModel>>>
    with
        $FutureModifier<List<CategoryModel>>,
        $FutureProvider<List<CategoryModel>> {
  const CategoriesProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'categoriesProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$categoriesHash();

  @$internal
  @override
  $FutureProviderElement<List<CategoryModel>> $createElement(
          $ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<List<CategoryModel>> create(Ref ref) {
    return categories(ref);
  }
}

String _$categoriesHash() => r'60ef1bf8e4691bc0f515affa86514ee57cfde743';

@ProviderFor(categoryContents)
const categoryContentsProvider = CategoryContentsFamily._();

final class CategoryContentsProvider extends $FunctionalProvider<
        AsyncValue<PaginatedContentResponse>,
        PaginatedContentResponse,
        FutureOr<PaginatedContentResponse>>
    with
        $FutureModifier<PaginatedContentResponse>,
        $FutureProvider<PaginatedContentResponse> {
  const CategoryContentsProvider._(
      {required CategoryContentsFamily super.from,
      required (
        int, {
        int page,
      })
          super.argument})
      : super(
          retry: null,
          name: r'categoryContentsProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$categoryContentsHash();

  @override
  String toString() {
    return r'categoryContentsProvider'
        ''
        '$argument';
  }

  @$internal
  @override
  $FutureProviderElement<PaginatedContentResponse> $createElement(
          $ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<PaginatedContentResponse> create(Ref ref) {
    final argument = this.argument as (
      int, {
      int page,
    });
    return categoryContents(
      ref,
      argument.$1,
      page: argument.page,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryContentsProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$categoryContentsHash() => r'a9fc3c45521f37e645682f45b4a072b3d11658c4';

final class CategoryContentsFamily extends $Family
    with
        $FunctionalFamilyOverride<
            FutureOr<PaginatedContentResponse>,
            (
              int, {
              int page,
            })> {
  const CategoryContentsFamily._()
      : super(
          retry: null,
          name: r'categoryContentsProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: false,
        );

  CategoryContentsProvider call(
    int categoryId, {
    int page = 1,
  }) =>
      CategoryContentsProvider._(argument: (
        categoryId,
        page: page,
      ), from: this);

  @override
  String toString() => r'categoryContentsProvider';
}

@ProviderFor(PaginatedCategoryContents)
const paginatedCategoryContentsProvider = PaginatedCategoryContentsFamily._();

final class PaginatedCategoryContentsProvider extends $AsyncNotifierProvider<
    PaginatedCategoryContents, PaginatedContentState> {
  const PaginatedCategoryContentsProvider._(
      {required PaginatedCategoryContentsFamily super.from,
      required int super.argument})
      : super(
          retry: null,
          name: r'paginatedCategoryContentsProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$paginatedCategoryContentsHash();

  @override
  String toString() {
    return r'paginatedCategoryContentsProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  PaginatedCategoryContents create() => PaginatedCategoryContents();

  @override
  bool operator ==(Object other) {
    return other is PaginatedCategoryContentsProvider &&
        other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$paginatedCategoryContentsHash() =>
    r'b45e13489e0e546e1dfc3ada0729c74b4253f1a5';

final class PaginatedCategoryContentsFamily extends $Family
    with
        $ClassFamilyOverride<
            PaginatedCategoryContents,
            AsyncValue<PaginatedContentState>,
            PaginatedContentState,
            FutureOr<PaginatedContentState>,
            int> {
  const PaginatedCategoryContentsFamily._()
      : super(
          retry: null,
          name: r'paginatedCategoryContentsProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  PaginatedCategoryContentsProvider call(
    int categoryId,
  ) =>
      PaginatedCategoryContentsProvider._(argument: categoryId, from: this);

  @override
  String toString() => r'paginatedCategoryContentsProvider';
}

abstract class _$PaginatedCategoryContents
    extends $AsyncNotifier<PaginatedContentState> {
  late final _$args = ref.$arg as int;
  int get categoryId => _$args;

  FutureOr<PaginatedContentState> build(
    int categoryId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref = this.ref
        as $Ref<AsyncValue<PaginatedContentState>, PaginatedContentState>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<PaginatedContentState>, PaginatedContentState>,
        AsyncValue<PaginatedContentState>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(contentDetails)
const contentDetailsProvider = ContentDetailsFamily._();

final class ContentDetailsProvider extends $FunctionalProvider<
        AsyncValue<ContentModel>, ContentModel, FutureOr<ContentModel>>
    with $FutureModifier<ContentModel>, $FutureProvider<ContentModel> {
  const ContentDetailsProvider._(
      {required ContentDetailsFamily super.from, required int super.argument})
      : super(
          retry: null,
          name: r'contentDetailsProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$contentDetailsHash();

  @override
  String toString() {
    return r'contentDetailsProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $FutureProviderElement<ContentModel> $createElement(
          $ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<ContentModel> create(Ref ref) {
    final argument = this.argument as int;
    return contentDetails(
      ref,
      argument,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is ContentDetailsProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$contentDetailsHash() => r'fc47178d90a877ffff8ada2caea5171e6b72fff5';

final class ContentDetailsFamily extends $Family
    with $FunctionalFamilyOverride<FutureOr<ContentModel>, int> {
  const ContentDetailsFamily._()
      : super(
          retry: null,
          name: r'contentDetailsProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  ContentDetailsProvider call(
    int contentId,
  ) =>
      ContentDetailsProvider._(argument: contentId, from: this);

  @override
  String toString() => r'contentDetailsProvider';
}

@ProviderFor(categoryName)
const categoryNameProvider = CategoryNameFamily._();

final class CategoryNameProvider
    extends $FunctionalProvider<AsyncValue<String>, String, FutureOr<String>>
    with $FutureModifier<String>, $FutureProvider<String> {
  const CategoryNameProvider._(
      {required CategoryNameFamily super.from, required int super.argument})
      : super(
          retry: null,
          name: r'categoryNameProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$categoryNameHash();

  @override
  String toString() {
    return r'categoryNameProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $FutureProviderElement<String> $createElement($ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<String> create(Ref ref) {
    final argument = this.argument as int;
    return categoryName(
      ref,
      argument,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryNameProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$categoryNameHash() => r'53c7befb187a9f9f0cdc328546b3b90b8b621283';

final class CategoryNameFamily extends $Family
    with $FunctionalFamilyOverride<FutureOr<String>, int> {
  const CategoryNameFamily._()
      : super(
          retry: null,
          name: r'categoryNameProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  CategoryNameProvider call(
    int contentId,
  ) =>
      CategoryNameProvider._(argument: contentId, from: this);

  @override
  String toString() => r'categoryNameProvider';
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
