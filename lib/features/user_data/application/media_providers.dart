import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/models/api_response.dart';
import '../../../data/models/category_model.dart';
import '../../../data/models/content_model.dart';
import '../../../data/repository/media_repository.dart';
import '../../../data/repository/media_repository_interface.dart';

part 'media_providers.g.dart';

// Provider to fetch the hierarchical categories
@Riverpod(keepAlive: true) // Changed to keepAlive: true
Future<List<CategoryModel>> categories(Ref ref) {
  final MediaRepositoryInterface mediaRepo = ref.watch(mediaRepositoryProvider);
  return mediaRepo.getCategories();
}

// Provider to fetch content for a specific category
@Riverpod(keepAlive: true)
Future<PaginatedContentResponse> categoryContents(Ref ref, int categoryId) {
  final MediaRepositoryInterface mediaRepo = ref.watch(mediaRepositoryProvider);
  return mediaRepo.getContentsForCategory(categoryId);
}

// Provider to get the details of a single content item by its ID.
@riverpod
Future<ContentModel> contentDetails(Ref ref, int contentId) {
  final MediaRepositoryInterface mediaRepo = ref.watch(mediaRepositoryProvider);
  return mediaRepo.getContentById(contentId);
}

// Provider to get the category name for a content item
@riverpod
Future<String> categoryName(Ref ref, int contentId) async {
  // First get the content details to get the category ID
  final ContentModel content =
      await ref.watch(contentDetailsProvider(contentId).future);
  final int categoryId = content.categoryId;

  // Then get all categories to find the matching one
  final List<CategoryModel> allCategories =
      await ref.watch(categoriesProvider.future);

  // Helper function to find a category by ID in a nested structure
  String findCategoryName(List<CategoryModel> categories, int targetId) {
    for (final CategoryModel category in categories) {
      if (category.id == targetId) {
        return category.name;
      }

      // Check subcategories if they exist
      if (category.subcategories != null &&
          category.subcategories!.isNotEmpty) {
        final String subCategoryName =
            findCategoryName(category.subcategories!, targetId);
        if (subCategoryName.isNotEmpty) {
          return subCategoryName;
        }
      }
    }
    return '';
  }

  final String name = findCategoryName(allCategories, categoryId);
  return name.isNotEmpty ? name : 'غير مصنف';
}
