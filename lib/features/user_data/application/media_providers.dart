import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/models/api_response.dart';
import '../../../data/models/category_model.dart';
import '../../../data/models/content_model.dart';
import '../../../data/repository/media_repository.dart';
import '../../../data/repository/media_repository_interface.dart';

part 'media_providers.g.dart';

// Provider to fetch the hierarchical categories
@Riverpod(keepAlive: true) // Changed to keepAlive: true
Future<List<CategoryModel>> categories(Ref ref) {
  final MediaRepositoryInterface mediaRepo = ref.watch(mediaRepositoryProvider);
  return mediaRepo.getCategories();
}

// Provider to fetch content for a specific category with pagination
@Riverpod(keepAlive: true)
Future<PaginatedContentResponse> categoryContents(Ref ref, int categoryId,
    {int page = 1}) {
  final MediaRepositoryInterface mediaRepo = ref.watch(mediaRepositoryProvider);
  return mediaRepo.getContentsForCategory(categoryId, page: page);
}

// State class for managing paginated content
class PaginatedContentState {
  const PaginatedContentState({
    required this.contents,
    required this.pagination,
    required this.category,
    this.isLoadingMore = false,
    this.hasError = false,
    this.errorMessage,
  });

  final List<ContentModel> contents;
  final PaginationInfo pagination;
  final CategoryModel category;
  final bool isLoadingMore;
  final bool hasError;
  final String? errorMessage;

  PaginatedContentState copyWith({
    List<ContentModel>? contents,
    PaginationInfo? pagination,
    CategoryModel? category,
    bool? isLoadingMore,
    bool? hasError,
    String? errorMessage,
  }) {
    return PaginatedContentState(
      contents: contents ?? this.contents,
      pagination: pagination ?? this.pagination,
      category: category ?? this.category,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasError: hasError ?? this.hasError,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

// Provider for managing paginated content with infinite scroll
@riverpod
class PaginatedCategoryContents extends _$PaginatedCategoryContents {
  @override
  Future<PaginatedContentState> build(int categoryId) async {
    final MediaRepositoryInterface mediaRepo =
        ref.watch(mediaRepositoryProvider);
    final PaginatedContentResponse response =
        await mediaRepo.getContentsForCategory(categoryId);

    return PaginatedContentState(
      contents: response.data.contents,
      pagination: response.data.pagination,
      category: response.data.category,
    );
  }

  Future<void> loadMore() async {
    final PaginatedContentState currentState = await future;

    // Don't load if already loading or no more pages
    if (currentState.isLoadingMore || !currentState.pagination.hasMore) {
      return;
    }

    // Set loading state
    state = AsyncValue<PaginatedContentState>.data(
        currentState.copyWith(isLoadingMore: true, hasError: false));

    try {
      final MediaRepositoryInterface mediaRepo =
          ref.read(mediaRepositoryProvider);
      final int nextPage = currentState.pagination.currentPage + 1;
      final PaginatedContentResponse response =
          await mediaRepo.getContentsForCategory(categoryId, page: nextPage);

      // Combine existing contents with new ones
      final List<ContentModel> allContents = <ContentModel>[
        ...currentState.contents,
        ...response.data.contents
      ];

      state = AsyncValue<PaginatedContentState>.data(PaginatedContentState(
        contents: allContents,
        pagination: response.data.pagination,
        category: response.data.category,
      ));
    } catch (error) {
      state = AsyncValue<PaginatedContentState>.data(currentState.copyWith(
        isLoadingMore: false,
        hasError: true,
        errorMessage: error.toString(),
      ));
    }
  }

  Future<void> refresh() async {
    state = const AsyncValue<PaginatedContentState>.loading();
    state = await AsyncValue.guard(() async {
      final MediaRepositoryInterface mediaRepo =
          ref.read(mediaRepositoryProvider);
      final PaginatedContentResponse response =
          await mediaRepo.getContentsForCategory(categoryId);

      return PaginatedContentState(
        contents: response.data.contents,
        pagination: response.data.pagination,
        category: response.data.category,
      );
    });
  }
}

// Provider to get the details of a single content item by its ID.
@riverpod
Future<ContentModel> contentDetails(Ref ref, int contentId) {
  final MediaRepositoryInterface mediaRepo = ref.watch(mediaRepositoryProvider);
  return mediaRepo.getContentById(contentId);
}

// Provider to get the category name for a content item
@riverpod
Future<String> categoryName(Ref ref, int contentId) async {
  // First get the content details to get the category ID
  final ContentModel content =
      await ref.watch(contentDetailsProvider(contentId).future);
  final int categoryId = content.categoryId;

  // Then get all categories to find the matching one
  final List<CategoryModel> allCategories =
      await ref.watch(categoriesProvider.future);

  // Helper function to find a category by ID in a nested structure
  String findCategoryName(List<CategoryModel> categories, int targetId) {
    for (final CategoryModel category in categories) {
      if (category.id == targetId) {
        return category.name;
      }

      // Check subcategories if they exist
      if (category.subcategories != null &&
          category.subcategories!.isNotEmpty) {
        final String subCategoryName =
            findCategoryName(category.subcategories!, targetId);
        if (subCategoryName.isNotEmpty) {
          return subCategoryName;
        }
      }
    }
    return '';
  }

  final String name = findCategoryName(allCategories, categoryId);
  return name.isNotEmpty ? name : 'غير مصنف';
}
