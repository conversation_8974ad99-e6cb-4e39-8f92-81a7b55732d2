// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_data_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(Favorites)
const favoritesProvider = FavoritesProvider._();

final class FavoritesProvider
    extends $AsyncNotifierProvider<Favorites, List<FavoriteItem>> {
  const FavoritesProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'favoritesProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$favoritesHash();

  @$internal
  @override
  Favorites create() => Favorites();
}

String _$favoritesHash() => r'a70b6a11d9b0c946b9961c387dc02983677c98b6';

abstract class _$Favorites extends $AsyncNotifier<List<FavoriteItem>> {
  FutureOr<List<FavoriteItem>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref as $Ref<AsyncValue<List<FavoriteItem>>, List<FavoriteItem>>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<List<FavoriteItem>>, List<FavoriteItem>>,
        AsyncValue<List<FavoriteItem>>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(isFavorite)
const isFavoriteProvider = IsFavoriteFamily._();

final class IsFavoriteProvider
    extends $FunctionalProvider<AsyncValue<bool>, bool, FutureOr<bool>>
    with $FutureModifier<bool>, $FutureProvider<bool> {
  const IsFavoriteProvider._(
      {required IsFavoriteFamily super.from, required String super.argument})
      : super(
          retry: null,
          name: r'isFavoriteProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$isFavoriteHash();

  @override
  String toString() {
    return r'isFavoriteProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $FutureProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<bool> create(Ref ref) {
    final argument = this.argument as String;
    return isFavorite(
      ref,
      argument,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is IsFavoriteProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$isFavoriteHash() => r'8fd9d6c1d3fc4b21eeb899fb02ef183241f11c64';

final class IsFavoriteFamily extends $Family
    with $FunctionalFamilyOverride<FutureOr<bool>, String> {
  const IsFavoriteFamily._()
      : super(
          retry: null,
          name: r'isFavoriteProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: false,
        );

  IsFavoriteProvider call(
    String itemId,
  ) =>
      IsFavoriteProvider._(argument: itemId, from: this);

  @override
  String toString() => r'isFavoriteProvider';
}

@ProviderFor(Bookmarks)
const bookmarksProvider = BookmarksProvider._();

final class BookmarksProvider
    extends $AsyncNotifierProvider<Bookmarks, List<BookmarkItem>> {
  const BookmarksProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'bookmarksProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$bookmarksHash();

  @$internal
  @override
  Bookmarks create() => Bookmarks();
}

String _$bookmarksHash() => r'e3710fc33c75c48aeed190a0f0ad1c2ce1bb78b5';

abstract class _$Bookmarks extends $AsyncNotifier<List<BookmarkItem>> {
  FutureOr<List<BookmarkItem>> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref =
        this.ref as $Ref<AsyncValue<List<BookmarkItem>>, List<BookmarkItem>>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<AsyncValue<List<BookmarkItem>>, List<BookmarkItem>>,
        AsyncValue<List<BookmarkItem>>,
        Object?,
        Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(isBookmarked)
const isBookmarkedProvider = IsBookmarkedFamily._();

final class IsBookmarkedProvider
    extends $FunctionalProvider<AsyncValue<bool>, bool, FutureOr<bool>>
    with $FutureModifier<bool>, $FutureProvider<bool> {
  const IsBookmarkedProvider._(
      {required IsBookmarkedFamily super.from, required String super.argument})
      : super(
          retry: null,
          name: r'isBookmarkedProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$isBookmarkedHash();

  @override
  String toString() {
    return r'isBookmarkedProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  $FutureProviderElement<bool> $createElement($ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<bool> create(Ref ref) {
    final argument = this.argument as String;
    return isBookmarked(
      ref,
      argument,
    );
  }

  @override
  bool operator ==(Object other) {
    return other is IsBookmarkedProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$isBookmarkedHash() => r'f9c82334c932d0f51456494c4a96342464477ab4';

final class IsBookmarkedFamily extends $Family
    with $FunctionalFamilyOverride<FutureOr<bool>, String> {
  const IsBookmarkedFamily._()
      : super(
          retry: null,
          name: r'isBookmarkedProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: false,
        );

  IsBookmarkedProvider call(
    String itemId,
  ) =>
      IsBookmarkedProvider._(argument: itemId, from: this);

  @override
  String toString() => r'isBookmarkedProvider';
}

@ProviderFor(UserData)
const userDataProvider = UserDataProvider._();

final class UserDataProvider extends $NotifierProvider<UserData, void> {
  const UserDataProvider._()
      : super(
          from: null,
          argument: null,
          retry: null,
          name: r'userDataProvider',
          isAutoDispose: false,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$userDataHash();

  @$internal
  @override
  UserData create() => UserData();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(void value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<void>(value),
    );
  }
}

String _$userDataHash() => r'0a89230770558f2aeb109f8cb171c1b7adc05545';

abstract class _$UserData extends $Notifier<void> {
  void build();
  @$mustCallSuper
  @override
  void runBuild() {
    build();
    final ref = this.ref as $Ref<void, void>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<void, void>, void, Object?, Object?>;
    element.handleValue(ref, null);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
