import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/enums/media_type_enum.dart';
import '../../../data/models/user_data_models.dart';
import '../../../data/repository/user_data_repository.dart';
import '../../../domain/repositories/user_data_repository_interface.dart';

part 'user_data_providers.g.dart';

const String _defaultUserId = 'default_user';

@Riverpod(keepAlive: true)
class Favorites extends _$Favorites {
  @override
  Future<List<FavoriteItem>> build() {
    return ref
        .watch(userDataRepositoryProvider)
        .getFavorites(userId: _defaultUserId);
  }

  Future<void> toggleFavorite({
    required String itemId,
    required MediaType type,
  }) async {
    final UserDataRepositoryInterface repository =
        ref.read(userDataRepositoryProvider);
    await repository.toggleFavorite(
      userId: _defaultUserId,
      itemId: itemId,
      itemType: type,
    );
    ref.invalidate(isFavoriteProvider(itemId));
    ref.invalidateSelf();
    await future;
  }

  Future<void> clearFavorites() async {
    final UserDataRepositoryInterface repository =
        ref.read(userDataRepositoryProvider);
    await repository.clearUserData(
      userId: _defaultUserId,
      clearBookmarks: false,
    );
    ref.invalidate(isFavoriteProvider);
    ref.invalidateSelf();
  }
}

@Riverpod(keepAlive: true)
Future<bool> isFavorite(Ref ref, String itemId) {
  return ref.watch(userDataRepositoryProvider).isFavorite(
        userId: _defaultUserId,
        itemId: itemId,
      );
}

@Riverpod(keepAlive: true)
class Bookmarks extends _$Bookmarks {
  @override
  Future<List<BookmarkItem>> build() {
    return ref
        .watch(userDataRepositoryProvider)
        .getBookmarks(userId: _defaultUserId);
  }

  Future<void> toggleBookmark({
    required String itemId,
    required MediaType type,
  }) async {
    final UserDataRepositoryInterface repository =
        ref.read(userDataRepositoryProvider);
    await repository.toggleBookmark(
      userId: _defaultUserId,
      itemId: itemId,
      itemType: type,
    );
    ref.invalidate(isBookmarkedProvider(itemId));
    ref.invalidateSelf();
    await future;
  }

  Future<void> clearBookmarks() async {
    final UserDataRepositoryInterface repository =
        ref.read(userDataRepositoryProvider);
    await repository.clearUserData(
      userId: _defaultUserId,
      clearFavorites: false,
    );
    ref.invalidate(isBookmarkedProvider);
    ref.invalidateSelf();
  }
}

@Riverpod(keepAlive: true)
Future<bool> isBookmarked(Ref ref, String itemId) {
  return ref.watch(userDataRepositoryProvider).isBookmarked(
        userId: _defaultUserId,
        itemId: itemId,
      );
}

@Riverpod(keepAlive: true)
class UserData extends _$UserData {
  @override
  void build() {}

  Future<void> clearAllUserData() async {
    await ref
        .read(userDataRepositoryProvider)
        .clearUserData(userId: _defaultUserId);
    if (ref.mounted) {
      ref.invalidate(favoritesProvider);
      ref.invalidate(bookmarksProvider);
    }
  }
}
