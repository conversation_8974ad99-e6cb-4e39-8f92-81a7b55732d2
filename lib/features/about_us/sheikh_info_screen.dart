// lib/features/about_us/sheikh_info_screen.dart

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

import '../../gen/assets.gen.dart';
import 'about_us_screen.dart'; // We can reuse the CustomDivider from here

class SheikhInfoScreen extends StatelessWidget {
  const SheikhInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);
    final ColorScheme colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('نبذة عن الشيخ'),
      ),
      backgroundColor: colorScheme.surface,
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: <Widget>[
              // Profile Image with a decorative border
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: <BoxShadow>[
                    BoxShadow(
                      color: colorScheme.primary..withValues(alpha: 0.2),
                      blurRadius: 12,
                      spreadRadius: 4,
                    ),
                  ],
                ),
                child: CircleAvatar(
                  radius: 80,
                  backgroundColor: colorScheme.surface,
                  child: CircleAvatar(
                    radius: 75,
                    backgroundImage: AssetImage(Assets.img.drImage.path),
                  ),
                ),
              ),
              const Gap(24),

              // Name and Title
              Text(
                'فضيلة الشيخ أ.د. محمد بن فهد الفريح',
                textAlign: TextAlign.center,
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
              const Gap(8),
              Text(
                'أستاذ الفقه المقارن بالمعهد العالي للقضاء',
                textAlign: TextAlign.center,
                style: theme.textTheme.titleMedium?.copyWith(
                  color: colorScheme.secondary,
                ),
              ),
              const Gap(32),

              // Introduction Section
              _buildSection(
                context,
                title: 'المقدمة',
                content:
                    'هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة، لقد تم توليد هذا النص من مولد النص العربى، حيث يمكنك أن تولد مثل هذا النص أو العديد من النصوص الأخرى إضافة إلى زيادة عدد الحروف التى يولدها التطبيق.',
              ),
              const Gap(24),

              // Education Section
              _buildSection(
                context,
                title: 'النشأة والتعليم',
                content:
                    'إذا كنت تحتاج إلى عدد أكبر من الفقرات يتيح لك مولد النص العربى زيادة عدد الفقرات كما تريد، النص لن يبدو مقسما ولا يحوي أخطاء لغوية، مولد النص العربى مفيد لمصممي المواقع على وجه الخصوص، حيث يحتاج العميل فى كثير من الأحيان أن يطلع على صورة حقيقية لتصميم الموقع.',
              ),
              const Gap(24),

              // Work Section
              _buildSection(
                context,
                title: 'أعماله ومناصبه',
                content:
                    'ومن هنا وجب على المصمم أن يضع نصوصا مؤقتة على التصميم ليظهر للعميل الشكل كاملاً،دور مولد النص العربى أن يوفر على المصمم عناء البحث عن نص بديل لا علاقة له بالموضوع الذى يتحدث عنه التصميم فيظهر بشكل لا يليق.',
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper widget to build each section for a clean look
  Widget _buildSection(BuildContext context,
      {required String title, required String content}) {
    final ThemeData theme = Theme.of(context);
    final ColorScheme colorScheme = theme.colorScheme;

    return Column(
      children: <Widget>[
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Expanded(
                child: CustomDivider(
                    colors: <Color>[Colors.transparent, Colors.grey])),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                title,
                style: theme.textTheme.titleLarge?.copyWith(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const Expanded(
                child: CustomDivider(
                    colors: <Color>[Colors.grey, Colors.transparent])),
          ],
        ),
        const Gap(16),
        Text(
          content,
          textAlign: TextAlign.justify,
          textDirection: TextDirection.rtl,
          style: theme.textTheme.bodyLarge?.copyWith(
            height: 1.8,
            color: colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }
}
