import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';

import '../../../utils/context_extensions.dart';
import '../../common/svg_icon.dart';
import '../../common/universal_image.dart';
import '../../constants/colors.dart';
import '../../gen/assets.gen.dart';
import '../../utils/report_error_dialog.dart';
import '../home/<USER>/home.dart';

class AboutUsScreen extends ConsumerWidget {
  const AboutUsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('حول التطبيق'),
      ),
      backgroundColor: context.colorScheme.surface,
      body: Column(
        children: <Widget>[
          const ShaikArabicFont(showSearch: false),
          const Spacer(),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Column(
              children: <Widget>[
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  spacing: 10,
                  children: <Widget>[
                    const CustomDivider(
                      colors: <Color>[kPriColor, klightBlackColor],
                    ),
                    Text(
                      'حسابات التواصل الاجتماعي',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: kBlackColor, fontWeight: FontWeight.w600),
                    ),
                    const CustomDivider(
                      colors: <Color>[klightBlackColor, kPriColor],
                    ),
                  ],
                ),
                const Gap(20),
                Text(
                  'يمكنك متابعة فضيلة الشيخ محمد بن فهد بن عبدالعزيز الفريح على مواقع التواصل الاجتماعي التالية',
                  textAlign: TextAlign.center,
                  style: Theme.of(context)
                      .textTheme
                      .bodyLarge
                      ?.copyWith(color: kBlackColor, fontSize: 12),
                ),
                const Gap(20),
                Container(
                  height: 60,
                  color: kPriLightColor,
                  child: Row(
                    spacing: 42,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      SvgIcon(
                        image: Assets.svg.facebook,
                        height: 35,
                        width: 35,
                        orignalColor: true,
                      ),
                      SvgIcon(
                        image: Assets.svg.insta,
                        height: 35,
                        width: 35,
                        orignalColor: true,
                      ),
                      SvgIcon(
                        image: Assets.svg.youtube,
                        height: 30,
                        width: 35,
                        orignalColor: true,
                      ),
                      SvgIcon(
                        image: Assets.svg.ticktok,
                        height: 35,
                        width: 35,
                        orignalColor: true,
                      ),
                    ],
                  ),
                ),
                const Gap(30),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  spacing: 10,
                  children: <Widget>[
                    const CustomDivider(
                      colors: <Color>[kPriColor, klightBlackColor],
                    ),
                    Text(
                      'تطوير',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: kBlackColor, fontWeight: FontWeight.w600),
                    ),
                    const CustomDivider(
                      colors: <Color>[klightBlackColor, kPriColor],
                    ),
                  ],
                ),
                GestureDetector(
                  onTap: () => openWhatsApp(context),
                  child: UniversalImage(
                    path: Assets.img.programing.path,
                    width: 332,
                  ),
                )
              ],
            ),
          ),
          const Gap(40),
        ],
      ),
    );
  }
}

class CustomDivider extends StatelessWidget {
  const CustomDivider({super.key, required this.colors});
  final List<Color> colors;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: SizedBox(
        height: 1,
        child: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: colors,
              begin: Alignment.centerRight,
              end: Alignment.centerLeft,
            ),
          ),
        ),
      ),
    );
  }
}
