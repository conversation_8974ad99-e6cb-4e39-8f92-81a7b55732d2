import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../data/models/content_model.dart';
import '../../user_data/application/media_providers.dart';

part 'tweet_interaction_controller.g.dart';

@riverpod
class TweetLikeController extends _$TweetLikeController {
  @override
  bool build(String tweetId) {
    // In a real app, you might fetch the initial liked status from a repository.
    // For now, it defaults to false.
    return false;
  }

  void toggleLike() {
    state = !state;
    final TweetLikeCount likeCountNotifier =
        ref.read(tweetLikeCountProvider(tweetId).notifier);
    if (state) {
      likeCountNotifier.increment();
    } else {
      likeCountNotifier.decrement();
    }
  }
}

@riverpod
class TweetLikeCount extends _$TweetLikeCount {
  @override
  int build(String tweetId) {
    // The API does not provide a like count, so we use the `visits`
    // count as a realistic initial value for demonstration.
    final AsyncValue<ContentModel> contentAsync =
        ref.watch(contentDetailsProvider(int.parse(tweetId)));
    return contentAsync.value?.visits ?? 0;
  }

  void increment() => state++;
  void decrement() {
    if (state > 0) {
      state--;
    }
  }
}

@riverpod
class TweetBookmarkController extends _$TweetBookmarkController {
  @override
  bool build(String tweetId) => false;
  void toggleBookmark() => state = !state;
}

@riverpod
class TweetRetweetController extends _$TweetRetweetController {
  @override
  bool build(String tweetId) => false;
  void toggleRetweet() => state = !state;
}

@riverpod
class TweetRetweetCount extends _$TweetRetweetCount {
  @override
  int build(String tweetId) {
    // Using a formula based on visits for a realistic-looking number.
    final AsyncValue<ContentModel> contentAsync =
        ref.watch(contentDetailsProvider(int.parse(tweetId)));
    return (contentAsync.value?.visits ?? 0) ~/ 4 + 2; // Example formula
  }
}

@riverpod
class TweetCommentCount extends _$TweetCommentCount {
  @override
  int build(String tweetId) => 0; // Not implemented from API
}
