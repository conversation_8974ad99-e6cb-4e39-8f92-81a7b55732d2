// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tweet_interaction_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@Provider<PERSON>or(TweetLikeController)
const tweetLikeControllerProvider = TweetLikeControllerFamily._();

final class TweetLikeControllerProvider
    extends $NotifierProvider<TweetLikeController, bool> {
  const TweetLikeControllerProvider._(
      {required TweetLikeControllerFamily super.from,
      required String super.argument})
      : super(
          retry: null,
          name: r'tweetLikeControllerProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$tweetLikeControllerHash();

  @override
  String toString() {
    return r'tweetLikeControllerProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  TweetLikeController create() => TweetLikeController();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is TweetLikeControllerProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$tweetLikeControllerHash() =>
    r'fca87375e8bfc4428631221060421b8e40a2cd36';

final class TweetLikeControllerFamily extends $Family
    with $ClassFamilyOverride<TweetLikeController, bool, bool, bool, String> {
  const TweetLikeControllerFamily._()
      : super(
          retry: null,
          name: r'tweetLikeControllerProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  TweetLikeControllerProvider call(
    String tweetId,
  ) =>
      TweetLikeControllerProvider._(argument: tweetId, from: this);

  @override
  String toString() => r'tweetLikeControllerProvider';
}

abstract class _$TweetLikeController extends $Notifier<bool> {
  late final _$args = ref.$arg as String;
  String get tweetId => _$args;

  bool build(
    String tweetId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref = this.ref as $Ref<bool, bool>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<bool, bool>, bool, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(TweetLikeCount)
const tweetLikeCountProvider = TweetLikeCountFamily._();

final class TweetLikeCountProvider
    extends $NotifierProvider<TweetLikeCount, int> {
  const TweetLikeCountProvider._(
      {required TweetLikeCountFamily super.from,
      required String super.argument})
      : super(
          retry: null,
          name: r'tweetLikeCountProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$tweetLikeCountHash();

  @override
  String toString() {
    return r'tweetLikeCountProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  TweetLikeCount create() => TweetLikeCount();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is TweetLikeCountProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$tweetLikeCountHash() => r'd8d3d0cdb3ccdbf3412f4e7e7ba66f78f2f88b5d';

final class TweetLikeCountFamily extends $Family
    with $ClassFamilyOverride<TweetLikeCount, int, int, int, String> {
  const TweetLikeCountFamily._()
      : super(
          retry: null,
          name: r'tweetLikeCountProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  TweetLikeCountProvider call(
    String tweetId,
  ) =>
      TweetLikeCountProvider._(argument: tweetId, from: this);

  @override
  String toString() => r'tweetLikeCountProvider';
}

abstract class _$TweetLikeCount extends $Notifier<int> {
  late final _$args = ref.$arg as String;
  String get tweetId => _$args;

  int build(
    String tweetId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref = this.ref as $Ref<int, int>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<int, int>, int, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(TweetBookmarkController)
const tweetBookmarkControllerProvider = TweetBookmarkControllerFamily._();

final class TweetBookmarkControllerProvider
    extends $NotifierProvider<TweetBookmarkController, bool> {
  const TweetBookmarkControllerProvider._(
      {required TweetBookmarkControllerFamily super.from,
      required String super.argument})
      : super(
          retry: null,
          name: r'tweetBookmarkControllerProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$tweetBookmarkControllerHash();

  @override
  String toString() {
    return r'tweetBookmarkControllerProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  TweetBookmarkController create() => TweetBookmarkController();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is TweetBookmarkControllerProvider &&
        other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$tweetBookmarkControllerHash() =>
    r'4bf12f123746440cb2f57029ede9240e8068e871';

final class TweetBookmarkControllerFamily extends $Family
    with
        $ClassFamilyOverride<TweetBookmarkController, bool, bool, bool,
            String> {
  const TweetBookmarkControllerFamily._()
      : super(
          retry: null,
          name: r'tweetBookmarkControllerProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  TweetBookmarkControllerProvider call(
    String tweetId,
  ) =>
      TweetBookmarkControllerProvider._(argument: tweetId, from: this);

  @override
  String toString() => r'tweetBookmarkControllerProvider';
}

abstract class _$TweetBookmarkController extends $Notifier<bool> {
  late final _$args = ref.$arg as String;
  String get tweetId => _$args;

  bool build(
    String tweetId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref = this.ref as $Ref<bool, bool>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<bool, bool>, bool, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(TweetRetweetController)
const tweetRetweetControllerProvider = TweetRetweetControllerFamily._();

final class TweetRetweetControllerProvider
    extends $NotifierProvider<TweetRetweetController, bool> {
  const TweetRetweetControllerProvider._(
      {required TweetRetweetControllerFamily super.from,
      required String super.argument})
      : super(
          retry: null,
          name: r'tweetRetweetControllerProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$tweetRetweetControllerHash();

  @override
  String toString() {
    return r'tweetRetweetControllerProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  TweetRetweetController create() => TweetRetweetController();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(bool value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<bool>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is TweetRetweetControllerProvider &&
        other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$tweetRetweetControllerHash() =>
    r'36f633d1b12358171e5a04aabfdf68bd586e7f6a';

final class TweetRetweetControllerFamily extends $Family
    with
        $ClassFamilyOverride<TweetRetweetController, bool, bool, bool, String> {
  const TweetRetweetControllerFamily._()
      : super(
          retry: null,
          name: r'tweetRetweetControllerProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  TweetRetweetControllerProvider call(
    String tweetId,
  ) =>
      TweetRetweetControllerProvider._(argument: tweetId, from: this);

  @override
  String toString() => r'tweetRetweetControllerProvider';
}

abstract class _$TweetRetweetController extends $Notifier<bool> {
  late final _$args = ref.$arg as String;
  String get tweetId => _$args;

  bool build(
    String tweetId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref = this.ref as $Ref<bool, bool>;
    final element = ref.element as $ClassProviderElement<
        AnyNotifier<bool, bool>, bool, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(TweetRetweetCount)
const tweetRetweetCountProvider = TweetRetweetCountFamily._();

final class TweetRetweetCountProvider
    extends $NotifierProvider<TweetRetweetCount, int> {
  const TweetRetweetCountProvider._(
      {required TweetRetweetCountFamily super.from,
      required String super.argument})
      : super(
          retry: null,
          name: r'tweetRetweetCountProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$tweetRetweetCountHash();

  @override
  String toString() {
    return r'tweetRetweetCountProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  TweetRetweetCount create() => TweetRetweetCount();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is TweetRetweetCountProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$tweetRetweetCountHash() => r'a7f3f9b718093699fc1611b7c6247d743093a047';

final class TweetRetweetCountFamily extends $Family
    with $ClassFamilyOverride<TweetRetweetCount, int, int, int, String> {
  const TweetRetweetCountFamily._()
      : super(
          retry: null,
          name: r'tweetRetweetCountProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  TweetRetweetCountProvider call(
    String tweetId,
  ) =>
      TweetRetweetCountProvider._(argument: tweetId, from: this);

  @override
  String toString() => r'tweetRetweetCountProvider';
}

abstract class _$TweetRetweetCount extends $Notifier<int> {
  late final _$args = ref.$arg as String;
  String get tweetId => _$args;

  int build(
    String tweetId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref = this.ref as $Ref<int, int>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<int, int>, int, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

@ProviderFor(TweetCommentCount)
const tweetCommentCountProvider = TweetCommentCountFamily._();

final class TweetCommentCountProvider
    extends $NotifierProvider<TweetCommentCount, int> {
  const TweetCommentCountProvider._(
      {required TweetCommentCountFamily super.from,
      required String super.argument})
      : super(
          retry: null,
          name: r'tweetCommentCountProvider',
          isAutoDispose: true,
          dependencies: null,
          $allTransitiveDependencies: null,
        );

  @override
  String debugGetCreateSourceHash() => _$tweetCommentCountHash();

  @override
  String toString() {
    return r'tweetCommentCountProvider'
        ''
        '($argument)';
  }

  @$internal
  @override
  TweetCommentCount create() => TweetCommentCount();

  /// {@macro riverpod.override_with_value}
  Override overrideWithValue(int value) {
    return $ProviderOverride(
      origin: this,
      providerOverride: $SyncValueProvider<int>(value),
    );
  }

  @override
  bool operator ==(Object other) {
    return other is TweetCommentCountProvider && other.argument == argument;
  }

  @override
  int get hashCode {
    return argument.hashCode;
  }
}

String _$tweetCommentCountHash() => r'3be362982a202702c4a13046e1e5196d4b0ff73f';

final class TweetCommentCountFamily extends $Family
    with $ClassFamilyOverride<TweetCommentCount, int, int, int, String> {
  const TweetCommentCountFamily._()
      : super(
          retry: null,
          name: r'tweetCommentCountProvider',
          dependencies: null,
          $allTransitiveDependencies: null,
          isAutoDispose: true,
        );

  TweetCommentCountProvider call(
    String tweetId,
  ) =>
      TweetCommentCountProvider._(argument: tweetId, from: this);

  @override
  String toString() => r'tweetCommentCountProvider';
}

abstract class _$TweetCommentCount extends $Notifier<int> {
  late final _$args = ref.$arg as String;
  String get tweetId => _$args;

  int build(
    String tweetId,
  );
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build(
      _$args,
    );
    final ref = this.ref as $Ref<int, int>;
    final element = ref.element
        as $ClassProviderElement<AnyNotifier<int, int>, int, Object?, Object?>;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
