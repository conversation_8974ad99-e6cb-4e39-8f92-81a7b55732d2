import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../data/models/api_response.dart';
import '../../../data/models/content_model.dart';
import '../../../routing/app_router.dart';
import '../../user_data/application/media_providers.dart';
import 'widgets/tweet_card.dart';

class TweetsListScreen extends ConsumerWidget {
  const TweetsListScreen({super.key});

  // FAKE DATA: We assume a category ID for "Social Media Posts".
  // From your API, the parent category ID is 6. You can choose a more specific one if needed.
  static const int socialMediaCategoryID = 6;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<PaginatedContentResponse> postsAsync =
        ref.watch(categoryContentsProvider(socialMediaCategoryID));

    return Scaffold(
      appBar: AppBar(title: const Text('أخبار ومقالات')),
      body: postsAsync.when(
        data: (PaginatedContentResponse paginatedResponse) {
          // FIX: Access the list of items via `paginatedResponse.data.contents`
          final List<ContentModel> tweetItems = paginatedResponse.data.contents
              .where((ContentModel item) => item.contentType == 'post')
              .toList();

          if (tweetItems.isEmpty) {
            return const Center(child: Text('No posts available'));
          }
          return ListView.builder(
            itemCount: tweetItems.length,
            itemBuilder: (BuildContext context, int index) {
              return TweetCard(
                tweet: tweetItems[index],
                onTap: () => context.pushNamed(
                  SGRoute.tweetDetails.name,
                  pathParameters: <String, String>{
                    'id': tweetItems[index].id.toString()
                  },
                ),
              );
            },
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (Object err, StackTrace stack) =>
            Center(child: Text('Error: $err')),
      ),
    );
  }
}
