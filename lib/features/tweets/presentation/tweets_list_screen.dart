import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'widgets/paginated_tweets_list.dart';

class TweetsListScreen extends ConsumerWidget {
  const TweetsListScreen({super.key});

  // FAKE DATA: We assume a category ID for "Social Media Posts".
  // From your API, the parent category ID is 6. You can choose a more specific one if needed.
  static const int socialMediaCategoryID = 6;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(title: const Text('أخبار ومقالات')),
      body: const PaginatedTweetsList(
        categoryId: socialMediaCategoryID,
      ),
    );
  }
}
