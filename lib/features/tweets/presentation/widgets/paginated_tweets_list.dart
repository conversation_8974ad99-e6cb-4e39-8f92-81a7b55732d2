import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../data/models/content_model.dart';
import '../../../../routing/app_router.dart';
import '../../../user_data/application/media_providers.dart';
import 'tweet_card.dart';

class PaginatedTweetsList extends ConsumerStatefulWidget {
  const PaginatedTweetsList({
    super.key,
    required this.categoryId,
    this.enableInfiniteScroll = true,
  });

  final int categoryId;
  final bool enableInfiniteScroll;

  @override
  ConsumerState<PaginatedTweetsList> createState() =>
      _PaginatedTweetsListState();
}

class _PaginatedTweetsListState extends ConsumerState<PaginatedTweetsList> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    if (widget.enableInfiniteScroll) {
      _scrollController.addListener(_onScroll);
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMore();
    }
  }

  void _loadMore() {
    ref
        .read(paginatedCategoryContentsProvider(widget.categoryId).notifier)
        .loadMore();
  }

  void _refresh() {
    ref
        .read(paginatedCategoryContentsProvider(widget.categoryId).notifier)
        .refresh();
  }

  void _navigateToTweetDetails(ContentModel tweet) {
    context.pushNamed(
      SGRoute.tweetDetails.name,
      pathParameters: <String, String>{'id': tweet.id.toString()},
    );
  }

  @override
  Widget build(BuildContext context) {
    final AsyncValue<PaginatedContentState> paginatedState =
        ref.watch(paginatedCategoryContentsProvider(widget.categoryId));

    return RefreshIndicator(
      onRefresh: () async => _refresh(),
      child: paginatedState.when(
        data: (PaginatedContentState state) => _buildTweetsList(state),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (Object error, StackTrace stack) => _buildErrorWidget(error),
      ),
    );
  }

  Widget _buildTweetsList(PaginatedContentState state) {
    // Filter only post content
    final List<ContentModel> tweetItems = state.contents
        .where((ContentModel item) => item.contentType == 'post')
        .toList();

    if (tweetItems.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد منشورات متاحة',
          style: TextStyle(fontSize: 16),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      itemCount: tweetItems.length + (state.pagination.hasMore ? 1 : 0),
      itemBuilder: (BuildContext context, int index) {
        if (index == tweetItems.length) {
          return _buildLoadMoreWidget(state);
        }

        final ContentModel tweet = tweetItems[index];
        return TweetCard(
          tweet: tweet,
          onTap: () => _navigateToTweetDetails(tweet),
        );
      },
    );
  }

  Widget _buildLoadMoreWidget(PaginatedContentState state) {
    if (state.isLoadingMore) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (state.hasError) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            Text(
              'خطأ في تحميل المزيد: ${state.errorMessage}',
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _loadMore,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildErrorWidget(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ: $error',
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _refresh,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }
}
