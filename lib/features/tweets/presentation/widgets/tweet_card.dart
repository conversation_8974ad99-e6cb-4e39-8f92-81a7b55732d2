import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:ionicons/ionicons.dart';

import '../../../../common/universal_image.dart';
import '../../../../constants/colors.dart';
import '../../../../data/models/content_model.dart';
import '../../../../gen/assets.gen.dart';
import '../../../media_player/application/services/service_providers.dart';
import '../../application/tweet_interaction_controller.dart';

class TweetCard extends ConsumerWidget {
  const TweetCard({
    required this.tweet,
    this.onTap,
    this.isDetailView = false,
    super.key,
  });

  final ContentModel tweet;
  final VoidCallback? onTap;
  final bool isDetailView;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final PostData? postData = tweet.postData;
    if (postData == null) {
      return const SizedBox.shrink();
    }

    final String tweetId = tweet.id.toString();

    // Watch the providers to get the current state
    final bool isLiked = ref.watch(tweetLikeControllerProvider(tweetId));
    final int likeCount = ref.watch(tweetLikeCountProvider(tweetId));
    final int retweetCount = ref.watch(tweetRetweetCountProvider(tweetId));

    final String formattedDate = DateFormat('h:mm a · MMM d, yyyy', 'ar')
        .format(postData.publishedAt ?? tweet.createdAt);

    return GestureDetector(
      onTap: isDetailView ? null : onTap,
      child: Container(
        margin: EdgeInsets.all(isDetailView ? 0 : 12),
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(isDetailView ? 0 : 12),
          border: isDetailView ? null : Border.all(color: Colors.grey.shade200),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                CircleAvatar(
                  radius: 24,
                  backgroundImage: AssetImage(Assets.img.drImage.path),
                ),
                const SizedBox(width: 12),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Text('أ.د. محمد بن فهد الفريح',
                              style: TextStyle(
                                  fontWeight: FontWeight.bold, fontSize: 16)),
                          SizedBox(width: 4),
                          Icon(Icons.verified, color: kPrimaryLight, size: 16),
                        ],
                      ),
                      Text('@dralfarih',
                          style: TextStyle(color: Colors.grey, fontSize: 14)),
                    ],
                  ),
                ),
                // if (isDetailView)
                //   IconButton(
                //       icon: const Icon(Icons.more_horiz), onPressed: () {}),
              ],
            ),
            const SizedBox(height: 12),
            Text(postData.text,
                style: const TextStyle(fontSize: 16, height: 1.4)),
            if (postData.imageUrl != null)
              Padding(
                padding: const EdgeInsets.only(top: 12.0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: UniversalImage(path: postData.imageUrl!),
                ),
              ),
            const SizedBox(height: 12),
            Text(formattedDate,
                style: TextStyle(color: Colors.grey[600], fontSize: 14)),
            if (isDetailView) ...<Widget>[
              const Divider(height: 24),
              Row(
                children: <Widget>[
                  _TweetStat(count: '$retweetCount', label: 'إعادة تغريد'),
                  const SizedBox(width: 24),
                  _TweetStat(count: '$likeCount', label: 'إعجاب'),
                ],
              ),
            ],
            const Divider(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: <Widget>[
                _TweetAction(
                  icon: Ionicons.chatbubble_outline,
                  label: 'تعليق',
                  onTap: () => ref
                      .read(interactionServiceProvider)
                      .openInBrowser(tweet.postData!.url!),
                ),
                _TweetAction(
                  icon: Ionicons.repeat,
                  label: 'إعادة',
                  onTap: () => ref
                      .read(interactionServiceProvider)
                      .openInBrowser(tweet.postData!.url!),
                ),
                _TweetAction(
                  icon: isLiked ? Ionicons.heart : Ionicons.heart_outline,
                  label: likeCount.toString(),
                  onTap: () => ref
                      .read(tweetLikeControllerProvider(tweetId).notifier)
                      .toggleLike(),
                  isActive: isLiked,
                  activeColor: Colors.red,
                ),
                _TweetAction(
                  icon: Ionicons.share_outline,
                  label: 'مشاركة',
                  onTap: () =>
                      ref.read(interactionServiceProvider).shareContent(tweet),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _TweetStat extends StatelessWidget {
  const _TweetStat({required this.count, required this.label});
  final String count;
  final String label;
  @override
  Widget build(BuildContext context) {
    return Row(
      children: <Widget>[
        Text(count,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14)),
        const SizedBox(width: 4),
        Text(label, style: TextStyle(color: Colors.grey[600], fontSize: 14)),
      ],
    );
  }
}

class _TweetAction extends StatelessWidget {
  const _TweetAction({
    required this.icon,
    required this.label,
    required this.onTap,
    this.isActive = false,
    this.activeColor,
  });

  final IconData icon;
  final String label;
  final VoidCallback onTap;
  final bool isActive;
  final Color? activeColor;

  @override
  Widget build(BuildContext context) {
    final Color? color = isActive
        ? activeColor ?? Theme.of(context).primaryColor
        : Colors.grey[700];
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
        child: Row(
          children: <Widget>[
            Icon(icon, size: 18, color: color),
            if (label.isNotEmpty) ...<Widget>[
              const SizedBox(width: 8),
              Text(label, style: TextStyle(fontSize: 13, color: color)),
            ],
          ],
        ),
      ),
    );
  }
}
