import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../common/widgets/category_app_bar.dart';
import '../../../data/models/content_model.dart';
import '../../user_data/application/media_providers.dart';
import 'widgets/tweet_card.dart';

class TweetDetailsScreen extends ConsumerWidget {
  const TweetDetailsScreen({required this.tweetId, super.key});
  final String tweetId;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final int? contentId = int.tryParse(tweetId);
    if (contentId == null) {
      return Scaffold(
          appBar: AppBar(),
          body: const Center(child: Text('Invalid Tweet ID')));
    }

    final AsyncValue<ContentModel> tweetAsync =
        ref.watch(contentDetailsProvider(contentId));

    return Scaffold(
      appBar: tweetAsync.when(
        data: (ContentModel tweet) => CategoryAppBar(
          contentId: tweet.id,
          title: 'التغريدة',
        ),
        loading: () => AppBar(title: const Text('التغريدة')),
        error: (_, __) => AppBar(title: const Text('التغريدة')),
      ),
      body: tweetAsync.when(
        data: (ContentModel tweet) => SingleChildScrollView(
          child: TweetCard(tweet: tweet, isDetailView: true),
        ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (Object err, StackTrace stack) =>
            Center(child: Text('Error: $err')),
      ),
    );
  }
}
