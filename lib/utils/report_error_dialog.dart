import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../constants/contact_info.dart';

/// Displays a dialog for the user to report an error via WhatsApp or Email.
Future<void> showReportErrorDialog(BuildContext context) async {
  return showDialog<void>(
    context: context,
    builder: (BuildContext dialogContext) {
      return AlertDialog(
        title: const Text(
          'ابلاغ عن خطأ',
          textAlign: TextAlign.center,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            const Text(
              'اختر طريقة التواصل المفضلة لديك:',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 20),
            _buildContactOption(
              context: dialogContext,
              icon: Icons.chat,
              title: 'عبر واتس اب',
              subtitle: 'تواصل معنا مباشرة',
              color: Colors.green,
              onTap: () => openWhatsApp(dialogContext),
            ),
            const SizedBox(height: 12),
            _buildContactOption(
              context: dialogContext,
              icon: Icons.email,
              title: 'عبر الإيميل',
              subtitle: 'أرسل لنا رسالة',
              color: const Color(0xFF1976D2),
              onTap: () => _openEmail(dialogContext),
            ),
          ],
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      );
    },
  );
}

// Private helper widget remains a top-level function but is only visible in this file.
Widget _buildContactOption({
  required BuildContext context,
  required IconData icon,
  required String title,
  required String subtitle,
  required Color color,
  required VoidCallback onTap,
}) {
  return InkWell(
    onTap: onTap,
    borderRadius: BorderRadius.circular(12),
    child: Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: color.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: <Widget>[
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: color,
            size: 16,
          ),
        ],
      ),
    ),
  );
}

// Private helper functions for launching URLs.
Future<void> openWhatsApp(BuildContext context) async {
  Navigator.of(context).pop(); // Close the dialog first

  final String whatsappUrl =
      'https://wa.me/${ContactInfo.whatsappNumber}?text=${Uri.encodeComponent(ContactInfo.whatsappMessage)}';

  try {
    final Uri uri = Uri.parse(whatsappUrl);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (context.mounted) {
        _showErrorSnackBar(
            context, 'لا يمكن فتح واتس اب. تأكد من تثبيت التطبيق.');
      }
    }
  } catch (e) {
    if (context.mounted) {
      _showErrorSnackBar(context, 'حدث خطأ أثناء فتح واتس اب');
    }
  }
}

Future<void> _openEmail(BuildContext context) async {
  Navigator.of(context).pop(); // Close the dialog first

  final String emailUrl =
      'mailto:${ContactInfo.supportEmail}?subject=${Uri.encodeComponent(ContactInfo.emailSubject)}&body=${Uri.encodeComponent(ContactInfo.emailBody)}';

  try {
    final Uri uri = Uri.parse(emailUrl);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (context.mounted) {
        _showErrorSnackBar(context, 'لا يمكن فتح تطبيق البريد الإلكتروني');
      }
    }
  } catch (e) {
    if (context.mounted) {
      _showErrorSnackBar(context, 'حدث خطأ أثناء فتح البريد الإلكتروني');
    }
  }
}

void _showErrorSnackBar(BuildContext context, String message) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      backgroundColor: Colors.red,
      behavior: SnackBarBehavior.floating,
    ),
  );
}
