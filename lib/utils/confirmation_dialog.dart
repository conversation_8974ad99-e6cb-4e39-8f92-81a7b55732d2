// ignore_for_file: avoid_classes_with_only_static_members

import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';

/// Generic confirmation dialog utility for consistent UI across the app
class ConfirmationDialog {
  /// Show a confirmation dialog with customizable content
  static Future<bool?> show({
    required BuildContext context,
    required String title,
    required String content,
    String confirmText = 'تأكيد',
    String cancelText = 'إلغاء',
    Color? confirmColor,
    IconData? icon,
    bool isDangerous = false,
  }) async {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: <Widget>[
              if (icon != null) ...<Widget>[
                Icon(
                  icon,
                  color: isDangerous
                      ? Colors.red
                      : confirmColor ?? Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
              ],
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isDangerous ? Colors.red : null,
                      ),
                ),
              ),
            ],
          ),
          content: Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                cancelText,
                style: TextStyle(
                  color: Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.6),
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: isDangerous
                    ? Colors.red
                    : confirmColor ?? Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(confirmText),
            ),
          ],
        );
      },
    );
  }

  /// Show a dangerous action confirmation dialog (red theme)
  static Future<bool?> showDangerous({
    required BuildContext context,
    required String title,
    required String content,
    String confirmText = 'حذف',
    String cancelText = 'إلغاء',
    IconData icon = Ionicons.warning,
  }) async {
    return show(
      context: context,
      title: title,
      content: content,
      confirmText: confirmText,
      cancelText: cancelText,
      icon: icon,
      isDangerous: true,
    );
  }

  /// Show a logout confirmation dialog
  static Future<bool?> showLogout({
    required BuildContext context,
  }) async {
    return show(
      context: context,
      title: 'تسجيل الخروج',
      content: 'هل أنت متأكد من أنك تريد تسجيل الخروج؟',
      confirmText: 'تسجيل الخروج',
      icon: Ionicons.log_out,
      isDangerous: true,
    );
  }

  /// Show a clear data confirmation dialog
  static Future<bool?> showClearData({
    required BuildContext context,
    String dataType = 'البيانات',
  }) async {
    return showDangerous(
      context: context,
      title: 'مسح $dataType',
      content:
          'هل أنت متأكد من أنك تريد مسح جميع $dataType؟ لا يمكن التراجع عن هذا الإجراء.',
      confirmText: 'مسح',
      icon: Ionicons.heart_dislike_circle_outline,
    );
  }

  /// Show a delete account confirmation dialog
  static Future<bool?> showDeleteAccount({
    required BuildContext context,
  }) async {
    return showDangerous(
      context: context,
      title: 'حذف الحساب',
      content:
          'هل أنت متأكد من أنك تريد حذف حسابك نهائياً؟ سيتم فقدان جميع بياناتك ولا يمكن التراجع عن هذا الإجراء.',
      confirmText: 'حذف الحساب',
      icon: Ionicons.person_remove,
    );
  }

  /// Show a clear favorites confirmation dialog
  static Future<bool?> showClearFavorites({
    required BuildContext context,
  }) async {
    return showClearData(
      context: context,
      dataType: 'المفضلة',
    );
  }

  /// Show a save changes confirmation dialog
  static Future<bool?> showSaveChanges({
    required BuildContext context,
  }) async {
    return show(
      context: context,
      title: 'حفظ التغييرات',
      content: 'هل تريد حفظ التغييرات التي قمت بها؟',
      confirmText: 'حفظ',
      icon: Ionicons.save,
    );
  }

  /// Show a discard changes confirmation dialog
  static Future<bool?> showDiscardChanges({
    required BuildContext context,
  }) async {
    return show(
      context: context,
      title: 'تجاهل التغييرات',
      content:
          'هل أنت متأكد من أنك تريد تجاهل التغييرات؟ ستفقد جميع التعديلات غير المحفوظة.',
      confirmText: 'تجاهل',
      icon: Ionicons.close,
      isDangerous: true,
    );
  }
}
