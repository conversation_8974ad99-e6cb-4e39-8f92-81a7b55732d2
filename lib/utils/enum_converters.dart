import '../data/enums/media_type_enum.dart';

MediaType mapContentTypeToEnum(String contentType) {
  switch (contentType.toLowerCase()) {
    case 'audio':
      return MediaType.audio;
    case 'video':
      return MediaType.video;
    case 'article':
      return MediaType.text;
    case 'book':
      return MediaType.pdf;
    case 'post':
      return MediaType.tweet;
    default:
      return MediaType.unknown;
  }
}
