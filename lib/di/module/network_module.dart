// ignore_for_file: always_specify_types

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get_storage/get_storage.dart';
import 'package:injectable/injectable.dart';

import '../../constants/endpoints.dart';
import '../../core/network/cache_busting_interceptor.dart';
import '../../core/network/error_interceptor.dart';
import '../../core/network/production_safe_interceptor.dart';
import '../../data/getstore/get_store_helper.dart';
import '../../my_app.dart';

@module
abstract class NetworkModule {
  @lazySingleton
  Dio provideDio(GetStoreHelper getStoreHelper) {
    final dio = Dio();

    dio
      ..options.baseUrl = Endpoints.baseUrl
      ..options.connectTimeout =
          const Duration(milliseconds: Endpoints.connectionTimeout)
      ..options.receiveTimeout =
          const Duration(milliseconds: Endpoints.receiveTimeout)
      ..options.headers = {
        'Content-Type': 'application/json; charset=utf-8',
        'Accept': 'application/json',
      };

    // REMOVED: The InterceptorsWrapper that added the auth token is deleted.

    dio.interceptors.add(ErrorInterceptor(scaffoldMessengerKey));

    if (kDebugMode) {
      dio.interceptors.add(ProductionSafeInterceptor());
      dio.interceptors.add(CacheBustingInterceptor());
    }

    return dio;
  }

  // --- THIS IS THE FIX ---
  @preResolve
  Future<GetStorage> provideGetStorage() async {
    // 2. Return a new instance of the initialized storage.
    return GetStorage();
  }
}
