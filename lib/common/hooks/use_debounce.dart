// lib/common/hooks/use_debounce.dart

import 'dart:async';

import 'package:flutter/foundation.dart' show ValueNotifier;
import 'package:flutter_hooks/flutter_hooks.dart';

/// A custom hook that debounces a value change.
/// It waits for a pause in the [value] changes before updating the returned value.
T useDebounce<T>(T value,
    [Duration delay = const Duration(milliseconds: 500)]) {
  // 1. Create a state for the debounced value.
  final ValueNotifier<T> debouncedValue = useState<T>(value);

  // 2. Use useEffect to handle the timer logic.
  useEffect(() {
    // Set a timer to update the debounced value after the specified delay.
    final Timer timer = Timer(
      delay,
      () {
        debouncedValue.value = value;
      },
    );
    // When the input 'value' changes, useEffect's cleanup function
    // is called, which cancels the previous timer.
    return () => timer.cancel();
  }, <Object?>[
    value
  ]); // The effect re-runs whenever the input 'value' changes.

  // 3. Return the debounced value.
  return debouncedValue.value;
}
