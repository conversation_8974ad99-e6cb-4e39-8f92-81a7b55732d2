import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/api_response.dart';
import '../../features/user_data/application/media_providers.dart';

/// A widget that displays pagination information at the bottom of content lists
class PaginationInfoBar extends ConsumerWidget {
  const PaginationInfoBar({
    super.key,
    required this.categoryId,
    this.backgroundColor,
    this.textColor,
    this.showLoadMoreButton = false,
  });

  final int categoryId;
  final Color? backgroundColor;
  final Color? textColor;
  final bool showLoadMoreButton;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<PaginatedContentState> paginatedState =
        ref.watch(paginatedCategoryContentsProvider(categoryId));

    return paginatedState.when(
      data: (PaginatedContentState state) => _buildInfoBar(context, ref, state),
      loading: () => const SizedBox.shrink(),
      error: (Object error, StackTrace stack) => const SizedBox.shrink(),
    );
  }

  Widget _buildInfoBar(
      BuildContext context, WidgetRef ref, PaginatedContentState state) {
    final PaginationInfo pagination = state.pagination;
    final int totalItems = pagination.total;
    final int currentPage = pagination.currentPage;
    final int totalPages = pagination.lastPage;
    final int itemsPerPage = pagination.perPage;

    final int startItem = (currentPage - 1) * itemsPerPage + 1;
    final int endItem =
        (pagination.to ?? (currentPage * itemsPerPage)).clamp(0, totalItems);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: backgroundColor ?? Theme.of(context).cardColor,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 0.5,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(
                'عرض $startItem-$endItem من $totalItems',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: textColor ??
                          Theme.of(context).textTheme.bodySmall?.color,
                    ),
              ),
              Text(
                'صفحة $currentPage من $totalPages',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: textColor ??
                          Theme.of(context).textTheme.bodySmall?.color,
                    ),
              ),
            ],
          ),
          if (showLoadMoreButton && pagination.hasMore) ...<Widget>[
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: state.isLoadingMore
                    ? null
                    : () => ref
                        .read(paginatedCategoryContentsProvider(categoryId)
                            .notifier)
                        .loadMore(),
                child: state.isLoadingMore
                    ? const SizedBox(
                        height: 16,
                        width: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Text('تحميل المزيد'),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// A compact version of the pagination info bar for use in app bars or headers
class CompactPaginationInfo extends ConsumerWidget {
  const CompactPaginationInfo({
    super.key,
    required this.categoryId,
    this.textStyle,
  });

  final int categoryId;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<PaginatedContentState> paginatedState =
        ref.watch(paginatedCategoryContentsProvider(categoryId));

    return paginatedState.when(
      data: (PaginatedContentState state) {
        final PaginationInfo pagination = state.pagination;
        return Text(
          '${pagination.currentPage}/${pagination.lastPage} (${pagination.total})',
          style: textStyle ?? Theme.of(context).textTheme.bodySmall,
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (Object error, StackTrace stack) => const SizedBox.shrink(),
    );
  }
}

/// A floating action button that shows pagination info and allows quick navigation
class PaginationFAB extends ConsumerWidget {
  const PaginationFAB({
    super.key,
    required this.categoryId,
    this.onTap,
  });

  final int categoryId;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<PaginatedContentState> paginatedState =
        ref.watch(paginatedCategoryContentsProvider(categoryId));

    return paginatedState.when(
      data: (PaginatedContentState state) {
        final PaginationInfo pagination = state.pagination;

        if (pagination.total <= pagination.perPage) {
          return const SizedBox.shrink();
        }

        return FloatingActionButton.extended(
          onPressed: onTap ?? () => _showPaginationDialog(context, ref, state),
          icon: const Icon(Icons.info_outline),
          label: Text('${pagination.currentPage}/${pagination.lastPage}'),
          backgroundColor:
              Theme.of(context).primaryColor.withValues(alpha: 0.9),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (Object error, StackTrace stack) => const SizedBox.shrink(),
    );
  }

  void _showPaginationDialog(
      BuildContext context, WidgetRef ref, PaginatedContentState state) {
    final PaginationInfo pagination = state.pagination;

    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('معلومات الصفحة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text('الصفحة الحالية: ${pagination.currentPage}'),
            Text('إجمالي الصفحات: ${pagination.lastPage}'),
            Text('العناصر في الصفحة: ${pagination.perPage}'),
            Text('إجمالي العناصر: ${pagination.total}'),
            if (pagination.hasMore)
              const Text('يوجد المزيد من العناصر للتحميل'),
          ],
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
          if (pagination.hasMore)
            ElevatedButton(
              onPressed: state.isLoadingMore
                  ? null
                  : () {
                      Navigator.of(context).pop();
                      ref
                          .read(paginatedCategoryContentsProvider(categoryId)
                              .notifier)
                          .loadMore();
                    },
              child: state.isLoadingMore
                  ? const SizedBox(
                      height: 16,
                      width: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('تحميل المزيد'),
            ),
        ],
      ),
    );
  }
}
