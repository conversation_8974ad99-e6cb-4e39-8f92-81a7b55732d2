import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/content_model.dart';
import '../../features/user_data/application/media_providers.dart';
// Removed unnecessary format_utils and constants/colors imports
// Removed unnecessary action_type_enum import
import 'media_list_item.dart';

enum UserDataItemType { favorite, bookmark }

class UserDataListItem extends ConsumerWidget {
  const UserDataListItem.favorite({
    required this.itemId,
    required this.dateTime,
    this.onRemove,
    super.key,
  })  : itemType = UserDataItemType.favorite,
        actionType = null,
        positionSeconds = null;

  const UserDataListItem.bookmark({
    required this.itemId,
    required this.dateTime,
    this.onRemove,
    super.key,
  })  : itemType = UserDataItemType.bookmark,
        actionType = null,
        positionSeconds = null;

  final String itemId;
  final DateTime dateTime;
  final UserDataItemType itemType;
  final Object?
      actionType; // Kept as Object? to avoid breaking change in enum - consider removing if not used
  final double?
      positionSeconds; // Kept as double? - consider removing if not used
  final VoidCallback?
      onRemove; // This callback now needs to be handled inside the card

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final int? contentId = int.tryParse(itemId);
    if (contentId == null) {
      return _buildUnavailableItem(context, 'معرّف العنصر غير صالح');
    }

    final AsyncValue<ContentModel> mediaItemAsync =
        ref.watch(contentDetailsProvider(contentId));

    return mediaItemAsync.when(
      data: (ContentModel mediaItem) => _buildDataItem(context, mediaItem),
      loading: () => const Card(
          margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: ListTile(
              leading: CircularProgressIndicator(),
              title: Text('جاري التحميل...'))),
      error: (Object e, StackTrace s) =>
          _buildUnavailableItem(context, 'المحتوى لم يعد متوفراً'),
    );
  }

  Widget _buildUnavailableItem(BuildContext context, String reason) {
    // MediaListItem no longer has an 'unavailable' factory.
    // Return a simple placeholder card for unavailable items.
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      child: ListTile(
        leading: const Icon(Icons.error_outline, color: Colors.grey),
        title: Text('عنصر غير متوفر ($itemId)'),
        subtitle: Text(reason),
        trailing: onRemove != null
            ? IconButton(
                icon: const Icon(Icons.delete_outline),
                tooltip: 'إزالة',
                onPressed: onRemove,
              )
            : null,
      ),
    );
  }

  Widget _buildDataItem(BuildContext context, ContentModel mediaItem) {
    // MediaListItem now handles its own default navigation based on content type.
    // We simply pass the item to MediaListItem and let its internal logic
    // (the _defaultNavigate method) handle the routing, including for reels.
    return MediaListItem(
      item: mediaItem,
    );
  }
}
