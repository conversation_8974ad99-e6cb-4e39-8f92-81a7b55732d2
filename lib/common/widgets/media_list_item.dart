// lib/common/widgets/media_list_item.dart
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../data/models/content_model.dart';
// Import the necessary card components
import '../../features/home/<USER>/media_card_components.dart';
import '../../features/tweets/presentation/widgets/tweet_card.dart';
import '../../routing/app_router.dart';

class MediaListItem extends StatelessWidget {
  const MediaListItem({
    required this.item,
    this.onTap,
    super.key,
  });

  final ContentModel item;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    // Wrap with Card and InkWell as ItemsCard used to do
    return Card(
      elevation: 0,
      color: Colors.transparent,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      clipBehavior: Clip.antiAlias,
      shape: const Border(
          bottom: BorderSide(
              color: Color(0xffD7D7D3), width: 2)), // Replaced kGrayColor
      child: InkWell(
        onTap: () {
          // If a custom onTap is provided, use it. Otherwise, use default navigation.
          if (onTap != null) {
            onTap!();
          } else {
            _defaultNavigate(context, item);
          }
        },
        child: Padding(
          padding: const EdgeInsets.all(2),
          child: _buildCardContent(context),
        ),
      ),
    );
  }

  // Moved and adapted from original ItemsCard._buildCardContent
  Widget _buildCardContent(BuildContext context) {
    switch (item.contentType) {
      case 'video':
        return VideoCardContent(item: item);
      case 'audio':
        return AudioCardContent(item: item);
      case 'book':
        return BookCardContent(
          item: item,
          hideImage: false,
        );
      case 'article':
        return ArticleCardContent(item: item);
      case 'post':
        return TweetCard(tweet: item);
      default:
        return DefaultCardContent(item: item);
    }
  }

  // Modified _defaultNavigate method to handle reels specifically
  void _defaultNavigate(BuildContext context, ContentModel item) {
    // Define the category ID for reels/short videos.
    // This should be consistent with shortVideosCategoryId in reels_provider.dart (currently 33).
    const int reelsCategoryId = 33;

    // Check if the content is a video and belongs to the reels category
    if (item.contentType == 'video' && item.categoryId == reelsCategoryId) {
      context.pushNamed(
        SGRoute.reels.name,
        extra: item, // Pass the ContentModel directly as extra data
      );
    } else {
      // Existing navigation logic for other content types
      switch (item.contentType) {
        case 'book':
          context.pushNamed(
            SGRoute.pdfInfo.name,
            pathParameters: <String, String>{'id': item.id.toString()},
          );
          break;
        case 'article':
          context.pushNamed(
            SGRoute.textMediaViewer.name,
            pathParameters: <String, String>{'id': item.id.toString()},
          );
          break;
        case 'post':
          context.pushNamed(
            SGRoute.tweetDetails.name,
            pathParameters: <String, String>{'id': item.id.toString()},
          );
          break;
        default:
          // This default case will now handle other videos (not reels) and audios,
          // directing them to the generic media player.
          context.pushNamed(
            SGRoute.mediaPlayer.name,
            pathParameters: <String, String>{'id': item.id.toString()},
          );
      }
    }
  }
}
