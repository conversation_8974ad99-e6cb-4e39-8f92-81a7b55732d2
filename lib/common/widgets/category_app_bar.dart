import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../features/user_data/application/media_providers.dart';
import '../../routing/app_router.dart';

class CategoryAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const CategoryAppBar({
    required this.contentId,
    this.title,
    this.actions,
    this.leading,
    super.key,
  });

  final int contentId;
  final String? title;
  final List<Widget>? actions;
  final Widget? leading;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<String> categoryNameAsync =
        ref.watch(categoryNameProvider(contentId));

    return AppBar(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          categoryNameAsync.when(
            data: (String categoryName) => Text(
              categoryName,
              overflow: TextOverflow.ellipsis,
            ),
            loading: () => const SizedBox(
              height: 14,
              width: 100,
              child: LinearProgressIndicator(
                minHeight: 2,
              ),
            ),
            error: (_, __) => const Text(
              'غير مصنف',
              style: TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
      leading: leading ??
          IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.canPop()
                ? context.pop()
                : context.goNamed(SGRoute.home.name),
          ),
      actions: actions,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
