import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../data/models/content_model.dart';
import '../../features/user_data/application/media_providers.dart';
import 'media_list_item.dart';

class PaginatedContentList extends ConsumerStatefulWidget {
  const PaginatedContentList({
    super.key,
    required this.categoryId,
    this.onItemTap,
    this.showLoadMoreButton = true,
    this.enableInfiniteScroll = true,
  });

  final int categoryId;
  final void Function(ContentModel)? onItemTap;
  final bool showLoadMoreButton;
  final bool enableInfiniteScroll;

  @override
  ConsumerState<PaginatedContentList> createState() =>
      _PaginatedContentListState();
}

class _PaginatedContentListState extends ConsumerState<PaginatedContentList> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    if (widget.enableInfiniteScroll) {
      _scrollController.addListener(_onScroll);
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      _loadMore();
    }
  }

  void _loadMore() {
    ref
        .read(paginatedCategoryContentsProvider(widget.categoryId).notifier)
        .loadMore();
  }

  void _refresh() {
    ref
        .read(paginatedCategoryContentsProvider(widget.categoryId).notifier)
        .refresh();
  }

  @override
  Widget build(BuildContext context) {
    final AsyncValue<PaginatedContentState> paginatedState =
        ref.watch(paginatedCategoryContentsProvider(widget.categoryId));

    return RefreshIndicator(
      onRefresh: () async => _refresh(),
      child: paginatedState.when(
        data: (PaginatedContentState state) => _buildContentList(state),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (Object error, StackTrace stack) => _buildErrorWidget(error),
      ),
    );
  }

  Widget _buildContentList(PaginatedContentState state) {
    if (state.contents.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد عناصر في هذه الفئة.',
          style: TextStyle(fontSize: 16),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      itemCount: state.contents.length + (state.pagination.hasMore ? 1 : 0),
      itemBuilder: (BuildContext context, int index) {
        if (index == state.contents.length) {
          return _buildLoadMoreWidget(state);
        }

        final ContentModel item = state.contents[index];
        return MediaListItem(
          item: item,
          onTap:
              widget.onItemTap != null ? () => widget.onItemTap!(item) : null,
        );
      },
    );
  }

  Widget _buildLoadMoreWidget(PaginatedContentState state) {
    if (state.isLoadingMore) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (state.hasError) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            Text(
              'خطأ في تحميل المزيد: ${state.errorMessage}',
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _loadMore,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (widget.showLoadMoreButton && !widget.enableInfiniteScroll) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: ElevatedButton(
            onPressed: _loadMore,
            child: const Text('تحميل المزيد'),
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildErrorWidget(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ: $error',
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _refresh,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }
}

// Widget for displaying pagination info
class PaginationInfo extends StatelessWidget {
  const PaginationInfo({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.itemsPerPage,
  });

  final int currentPage;
  final int totalPages;
  final int totalItems;
  final int itemsPerPage;

  @override
  Widget build(BuildContext context) {
    final int startItem = (currentPage - 1) * itemsPerPage + 1;
    final int endItem = (currentPage * itemsPerPage).clamp(0, totalItems);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            'عرض $startItem-$endItem من $totalItems',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          Text(
            'صفحة $currentPage من $totalPages',
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }
}

// Widget for pagination controls (previous/next buttons)
class PaginationControls extends StatelessWidget {
  const PaginationControls({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.onPageChanged,
    this.hasMore = false,
  });

  final int currentPage;
  final int totalPages;
  final bool hasMore;
  final void Function(int page) onPageChanged;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: <Widget>[
          ElevatedButton.icon(
            onPressed:
                currentPage > 1 ? () => onPageChanged(currentPage - 1) : null,
            icon: const Icon(Icons.arrow_back_ios),
            label: const Text('السابق'),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).dividerColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '$currentPage / $totalPages',
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ),
          ElevatedButton.icon(
            onPressed: hasMore ? () => onPageChanged(currentPage + 1) : null,
            icon: const Icon(Icons.arrow_forward_ios),
            label: const Text('التالي'),
          ),
        ],
      ),
    );
  }
}

// Sliver version for use in CustomScrollView
class SliverPaginatedContentList extends ConsumerStatefulWidget {
  const SliverPaginatedContentList({
    super.key,
    required this.categoryId,
    this.onItemTap,
    this.enableInfiniteScroll = true,
  });

  final int categoryId;
  final void Function(ContentModel)? onItemTap;
  final bool enableInfiniteScroll;

  @override
  ConsumerState<SliverPaginatedContentList> createState() =>
      _SliverPaginatedContentListState();
}

class _SliverPaginatedContentListState
    extends ConsumerState<SliverPaginatedContentList> {
  void _loadMore() {
    ref
        .read(paginatedCategoryContentsProvider(widget.categoryId).notifier)
        .loadMore();
  }

  @override
  Widget build(BuildContext context) {
    final AsyncValue<PaginatedContentState> paginatedState =
        ref.watch(paginatedCategoryContentsProvider(widget.categoryId));

    return paginatedState.when(
      data: (PaginatedContentState state) => _buildSliverContentList(state),
      loading: () => const SliverToBoxAdapter(
        child: Padding(
          padding: EdgeInsets.only(top: 20),
          child: Center(child: CircularProgressIndicator()),
        ),
      ),
      error: (Object error, StackTrace stack) => SliverToBoxAdapter(
        child: _buildErrorWidget(error),
      ),
    );
  }

  Widget _buildSliverContentList(PaginatedContentState state) {
    if (state.contents.isEmpty) {
      return const SliverToBoxAdapter(
        child: Center(
          child: Padding(
            padding: EdgeInsets.all(20.0),
            child: Text(
              'لا يوجد محتوى في هذا التصنيف.',
              style: TextStyle(fontSize: 16),
            ),
          ),
        ),
      );
    }

    return SliverList.builder(
      itemCount: state.contents.length + (state.pagination.hasMore ? 1 : 0),
      itemBuilder: (BuildContext context, int index) {
        if (index == state.contents.length) {
          return _buildLoadMoreWidget(state);
        }

        final ContentModel item = state.contents[index];
        return MediaListItem(
          item: item,
          onTap:
              widget.onItemTap != null ? () => widget.onItemTap!(item) : null,
        );
      },
    );
  }

  Widget _buildLoadMoreWidget(PaginatedContentState state) {
    if (state.isLoadingMore) {
      return const Padding(
        padding: EdgeInsets.all(16.0),
        child: Center(child: CircularProgressIndicator()),
      );
    }

    if (state.hasError) {
      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            Text(
              'خطأ في تحميل المزيد: ${state.errorMessage}',
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _loadMore,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (widget.enableInfiniteScroll && state.pagination.hasMore) {
      // Trigger load more automatically when this widget is built
      WidgetsBinding.instance.addPostFrameCallback((_) => _loadMore());
    }

    return const SizedBox.shrink();
  }

  Widget _buildErrorWidget(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'حدث خطأ: $error',
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              ref
                  .read(paginatedCategoryContentsProvider(widget.categoryId)
                      .notifier)
                  .refresh();
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }
}
