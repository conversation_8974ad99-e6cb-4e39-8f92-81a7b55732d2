import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';

import '../../data/enums/media_type_enum.dart';
import '../../features/user_data/application/user_data_providers.dart';

class BookmarkButton extends ConsumerWidget {
  const BookmarkButton({
    required this.itemId,
    required this.itemType,
    this.size = 24.0,
    this.color,
    this.activeColor = Colors.blue,
    super.key,
  });

  final String itemId;
  final MediaType itemType;
  final double size;
  final Color? color;
  final Color activeColor;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final AsyncValue<bool> isBookmarkedAsync =
        ref.watch(isBookmarkedProvider(itemId));

    return isBookmarkedAsync.when(
      data: (bool isBookmarked) {
        return IconButton(
          icon: Icon(
            isBookmarked ? Ionicons.bookmark : Ionicons.bookmark_outline,
            size: size,
            color: isBookmarked ? activeColor : color,
          ),
          onPressed: () async {
            // Toggle the bookmark status
            await ref.read(bookmarksProvider.notifier).toggleBookmark(
                  itemId: itemId,
                  type: itemType,
                );

            // Invalidate the providers to force UI refresh
            ref.invalidate(isBookmarkedProvider(itemId));
            ref.invalidate(bookmarksProvider);
          },
        );
      },
      loading: () => SizedBox(
        width: size,
        height: size,
        child: const CircularProgressIndicator.adaptive(
          strokeWidth: 2,
        ),
      ),
      error: (Object error, StackTrace stackTrace) => IconButton(
        icon: Icon(
          Ionicons.bookmark_outline,
          size: size,
          color: color,
        ),
        onPressed: () async {
          // Toggle the bookmark status
          await ref.read(bookmarksProvider.notifier).toggleBookmark(
                itemId: itemId,
                type: itemType,
              );

          // Invalidate the providers to force UI refresh
          ref.invalidate(isBookmarkedProvider(itemId));
          ref.invalidate(bookmarksProvider);
        },
      ),
    );
  }
}
