// lib/routing/app_router.dart
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:injectable/injectable.dart';

import '../features/about_us/about_us_screen.dart';
import '../features/about_us/sheikh_info_screen.dart';
import '../features/home/<USER>/category_items_page.dart';
import '../features/home/<USER>/home.dart';
import '../features/media_player/presentation/media_player_screen.dart';
import '../features/reels/presentation/reels_screen.dart';
import '../features/search/presentation/search_screen.dart';
import '../features/settings/presentation/bookmarks_screen.dart';
import '../features/settings/presentation/favorites_screen.dart';
import '../features/splash/presentation/splash_screen.dart';
import '../features/surah_player/presentation/surah_player_screen.dart';
import '../features/text_media_viewer/presentation/pdf_info_screen.dart';
import '../features/text_media_viewer/presentation/text_media_router_screen_riverpod.dart';
import '../features/tweets/presentation/tweet_details_screen.dart';
import '../features/tweets/presentation/tweets_list_screen.dart';
import 'fade_extension.dart';

enum SGRoute {
  splash,
  home,
  mediaPlayer,
  surahPlayer,
  textMediaViewer,
  tweets,
  favorites,
  bookmarks,

  search,
  aboutUs,
  pdfInfo,
  categoryItems,
  tweetDetails,
  reels,
  sheikhInfo,
  ;

  String get route => '/${toString().replaceAll('SGRoute.', '')}';
  String get name => toString().replaceAll('SGRoute.', '');
}

@Singleton()
class SGGoRouter {
  final GoRouter goRoute = GoRouter(
    initialLocation: SGRoute.splash.route,
    routes: <GoRoute>[
      GoRoute(
        path: SGRoute.splash.route,
        name: SGRoute.splash.name,
        builder: (BuildContext context, GoRouterState state) =>
            const SplashScreen(),
      ).fade(),
      GoRoute(
        path: SGRoute.home.route,
        name: SGRoute.home.name,
        builder: (BuildContext context, GoRouterState state) =>
            const HomeScreen(),
      ).fade(),
      GoRoute(
        path: SGRoute.aboutUs.route,
        name: SGRoute.aboutUs.name,
        builder: (BuildContext context, GoRouterState state) =>
            const AboutUsScreen(),
      ).fade(),
      GoRoute(
        path: SGRoute.search.route,
        name: SGRoute.search.name,
        builder: (BuildContext context, GoRouterState state) =>
            const SearchScreen(),
      ).fade(),
      GoRoute(
        path: SGRoute.favorites.route,
        name: SGRoute.favorites.name,
        builder: (BuildContext context, GoRouterState state) =>
            const FavoritesScreen(),
      ).fade(),
      GoRoute(
        path: SGRoute.bookmarks.route,
        name: SGRoute.bookmarks.name,
        builder: (BuildContext context, GoRouterState state) =>
            const BookmarksScreen(),
      ).fade(),
      GoRoute(
        path: '${SGRoute.surahPlayer.route}/:id',
        name: SGRoute.surahPlayer.name,
        builder: (BuildContext context, GoRouterState state) {
          final int? id = int.tryParse(state.pathParameters['id'] ?? '');
          if (id == null) {
            return const Scaffold(body: Center(child: Text('Invalid ID')));
          }
          return SurahPlayerScreen(surahId: id);
        },
      ).fade(),
      GoRoute(
        path: '${SGRoute.mediaPlayer.route}/:id',
        name: SGRoute.mediaPlayer.name,
        builder: (BuildContext context, GoRouterState state) {
          final int? id = int.tryParse(state.pathParameters['id'] ?? '');
          if (id == null) {
            return const Scaffold(body: Center(child: Text('Invalid ID')));
          }
          return MediaPlayerScreen(contentId: id);
        },
      ).fade(),
      GoRoute(
        path: '${SGRoute.textMediaViewer.route}/:id',
        name: SGRoute.textMediaViewer.name,
        builder: (BuildContext context, GoRouterState state) {
          final int? id = int.tryParse(state.pathParameters['id'] ?? '');
          if (id == null) {
            return const Scaffold(body: Center(child: Text('Invalid ID')));
          }
          return TextMediaRouterScreen(mediaId: id);
        },
      ).fade(),
      GoRoute(
        path: '${SGRoute.pdfInfo.route}/:id',
        name: SGRoute.pdfInfo.name,
        builder: (BuildContext context, GoRouterState state) {
          final int? id = int.tryParse(state.pathParameters['id'] ?? '');
          if (id == null) {
            return const Scaffold(body: Center(child: Text('Invalid ID')));
          }
          return PdfInfoScreen(mediaId: id);
        },
      ).fade(),
      GoRoute(
        path: SGRoute.reels.route,
        name: SGRoute.reels.name,
        builder: (BuildContext context, GoRouterState state) {
          // Allow passing either an int (index) or a ContentModel
          final dynamic extra = state.extra;

          return ReelsScreen(startingIndexOrContent: extra);
        },
      ).fade(),
      GoRoute(
        path: SGRoute.tweets.route,
        name: SGRoute.tweets.name,
        builder: (BuildContext context, GoRouterState state) =>
            const TweetsListScreen(),
      ).fade(),
      GoRoute(
        path: '${SGRoute.tweets.route}/:id',
        name: SGRoute.tweetDetails.name,
        builder: (BuildContext context, GoRouterState state) {
          final String id = state.pathParameters['id']!;
          return TweetDetailsScreen(tweetId: id);
        },
      ).fade(),
      GoRoute(
        path: '/category',
        name: SGRoute.categoryItems.name,
        builder: (BuildContext context, GoRouterState state) {
          final CategoryItemsPageArgs? args =
              state.extra as CategoryItemsPageArgs?;
          if (args == null) {
            return const Scaffold(
              body: Center(child: Text('Error: Missing category arguments')),
            );
          }
          return CategoryItemsPage(
            categoryId: args.categoryId,
            categoryName: args.categoryName,
          );
        },
      ).fade(),
      GoRoute(
        path: SGRoute.sheikhInfo.route,
        name: SGRoute.sheikhInfo.name,
        builder: (BuildContext context, GoRouterState state) =>
            const SheikhInfoScreen(),
      ).fade(),
    ],
  );
  GoRouter get getGoRouter => goRoute;
}
