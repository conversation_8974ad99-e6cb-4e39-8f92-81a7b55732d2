import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// A Dio interceptor that handles common API errors globally
class ErrorInterceptor extends Interceptor {
  ErrorInterceptor(this.scaffoldMessengerKey);
  final GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey;

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // Log the error in debug mode
    if (kDebugMode) {
      debugPrint('API Error: ${err.message}');
      debugPrint('Error type: ${err.type}');
      debugPrint('Status code: ${err.response?.statusCode}');
    }

    // Handle specific status codes
    if (err.response != null) {
      final int statusCode = err.response!.statusCode ?? 0;

      // 503 Service Unavailable - show server unavailable message
      if (statusCode == 503) {
        _showServerUnavailableMessage();
      }
      // 401 Unauthorized - could handle token refresh or logout here
      else if (statusCode == 401) {
        // Handle unauthorized access (e.g., token expired)
        // This could trigger a token refresh or logout flow
      }
      // 429 Too Many Requests - rate limiting
      else if (statusCode == 429) {
        _showErrorMessage(
            'تم تجاوز الحد المسموح من الطلبات، يرجى المحاولة لاحقاً');
      }
    }
    // Handle network connectivity errors
    else if (err.type == DioExceptionType.connectionTimeout ||
        err.type == DioExceptionType.sendTimeout ||
        err.type == DioExceptionType.receiveTimeout ||
        (err.error is SocketException)) {
      _showErrorMessage(
          'لا يمكن الاتصال بالخادم، يرجى التحقق من اتصال الإنترنت');
    }

    // Continue with the error handling
    super.onError(err, handler);
  }

  void _showServerUnavailableMessage() {
    scaffoldMessengerKey.currentState?.showSnackBar(
      const SnackBar(
        content: Text(
          'غير قادر على الاتصال بالسرفر بالوقت الحالي',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 16),
        ),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 5),
      ),
    );
  }

  void _showErrorMessage(String message) {
    scaffoldMessengerKey.currentState?.showSnackBar(
      SnackBar(
        content: Text(
          message,
          textAlign: TextAlign.center,
          style: const TextStyle(fontSize: 16),
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
      ),
    );
  }
}
