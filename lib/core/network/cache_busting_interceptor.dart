import 'package:dio/dio.dart';

/// A Dio interceptor to prevent API caching during development.
/// It adds cache-control headers and a unique timestamp to every GET request.
class CacheBustingInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Only modify GET requests, as other methods like POST are not typically cached.
    if (options.method.toUpperCase() == 'GET') {
      // 1. Add headers to tell caches not to store the response.
      options.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate';
      options.headers['Pragma'] = 'no-cache';
      options.headers['Expires'] = '0';

      // 2. Add a unique query parameter to make the URL different for each request.
      // This is a very effective way to "bust" intermediate caches.
      options.queryParameters['_'] = DateTime.now().millisecondsSinceEpoch;
    }

    // Allow the request to continue with the modified options.
    super.onRequest(options, handler);
  }
}
