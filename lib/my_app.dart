import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'config/theme/app_colors.dart';
import 'config/theme/theme_constants.dart';
import 'config/theme/theme_logic.dart';
import 'config/theme/theme_ui_model.dart';
import 'constants/colors.dart';
import 'di/components/service_locator.dart';
import 'gen/fonts.gen.dart';
import 'routing/app_router.dart';

// Global key for accessing the scaffold messenger from anywhere
final GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ThemeUiModel currentTheme = ref.watch(themeLogicProvider);
    return MaterialApp.router(
      scaffoldMessengerKey: scaffoldMessengerKey,
      // showPerformanceOverlay: true,
      routerConfig: getIt<SGGoRouter>().getGoRouter,

      /// Localization is not available for the title.
      title: 'محمد بن فهد الفريح',

      theme: FlexThemeData.light(
        fontFamily: FontFamily.cairo,
        scaffoldBackground: kPriColor,

        primaryTextTheme: const TextTheme(
          bodyLarge: TextStyle(fontFamily: FontFamily.cairo),
          bodyMedium: TextStyle(fontFamily: FontFamily.cairo),
          bodySmall: TextStyle(fontFamily: FontFamily.cairo),
        ),

        textTheme: Typography.material2021()
            .black
            .apply(
              fontFamily: FontFamily.cairo,
            )
            .copyWith(
              bodyLarge:
                  const TextStyle(fontFamily: FontFamily.cairo, height: 1.5),
              bodyMedium:
                  const TextStyle(fontFamily: FontFamily.cairo, height: 1.5),
              bodySmall:
                  const TextStyle(fontFamily: FontFamily.cairo, height: 1.5),
              displayLarge: const TextStyle(fontFamily: FontFamily.cairo),
              displayMedium: const TextStyle(fontFamily: FontFamily.cairo),
              displaySmall: const TextStyle(fontFamily: FontFamily.cairo),
              headlineLarge: const TextStyle(fontFamily: FontFamily.cairo),
              headlineMedium: const TextStyle(fontFamily: FontFamily.cairo),
              headlineSmall: const TextStyle(fontFamily: FontFamily.cairo),
              titleLarge: const TextStyle(fontFamily: FontFamily.cairo),
              titleMedium: const TextStyle(fontFamily: FontFamily.cairo),
              titleSmall: const TextStyle(fontFamily: FontFamily.cairo),
              labelLarge: const TextStyle(fontFamily: FontFamily.cairo),
              labelMedium: const TextStyle(fontFamily: FontFamily.cairo),
              labelSmall: const TextStyle(fontFamily: FontFamily.cairo),
            ),

        // Using custom colors
        colors: AppColors.lightScheme,
        // You can also use the full color scheme if needed
        // colorScheme: AppColors.lightColorScheme,
        // Component theme configurations for light mode.
        subThemesData: ThemeConstants.commonSubThemes,
        // Direct ThemeData properties.
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        cupertinoOverrideTheme: const CupertinoThemeData(applyThemeToAll: true),
      ),
      darkTheme: FlexThemeData.dark(
        fontFamily: FontFamily.cairo,
        primaryTextTheme: const TextTheme(
          bodyLarge: TextStyle(fontFamily: FontFamily.cairo),
          bodyMedium: TextStyle(fontFamily: FontFamily.cairo),
          bodySmall: TextStyle(fontFamily: FontFamily.cairo),
        ),
        textTheme: Typography.material2021()
            .white
            .apply(
              fontFamily: FontFamily.cairo,
            )
            .copyWith(
              bodyLarge:
                  const TextStyle(fontFamily: FontFamily.cairo, height: 1.5),
              bodyMedium:
                  const TextStyle(fontFamily: FontFamily.cairo, height: 1.5),
              bodySmall:
                  const TextStyle(fontFamily: FontFamily.cairo, height: 1.5),
              displayLarge: const TextStyle(fontFamily: FontFamily.cairo),
              displayMedium: const TextStyle(fontFamily: FontFamily.cairo),
              displaySmall: const TextStyle(fontFamily: FontFamily.cairo),
              headlineLarge: const TextStyle(fontFamily: FontFamily.cairo),
              headlineMedium: const TextStyle(fontFamily: FontFamily.cairo),
              headlineSmall: const TextStyle(fontFamily: FontFamily.cairo),
              titleLarge: const TextStyle(fontFamily: FontFamily.cairo),
              titleMedium: const TextStyle(fontFamily: FontFamily.cairo),
              titleSmall: const TextStyle(fontFamily: FontFamily.cairo),
              labelLarge: const TextStyle(fontFamily: FontFamily.cairo),
              labelMedium: const TextStyle(fontFamily: FontFamily.cairo),
              labelSmall: const TextStyle(fontFamily: FontFamily.cairo),
            ),
        // Using custom colors
        colors: AppColors.darkScheme,
        // You can also use the full color scheme if needed
        // colorScheme: AppColors.darkColorScheme,
        // Component theme configurations for dark mode.
        subThemesData: ThemeConstants.darkSubThemes,
        // Direct ThemeData properties.
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        cupertinoOverrideTheme: const CupertinoThemeData(applyThemeToAll: true),
      ),
      themeMode: currentTheme.themeMode,
      debugShowCheckedModeBanner: false,
      // Set Arabic as the default and only locale
      locale: const Locale('ar'),
      // Force RTL text direction
      builder: (BuildContext context, Widget? child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: child!,
        );
      },
      // Configure RTL text direction globally
    );
  }
}

Future<void> setPreferredOrientations() {
  return SystemChrome.setPreferredOrientations(<DeviceOrientation>[
    DeviceOrientation.portraitUp,
  ]);
}
